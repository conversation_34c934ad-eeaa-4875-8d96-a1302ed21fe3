const state = {
  connected: false,
  socket: null,
  websocketManager: null,
  // 设备断开连接超时管理
  deviceDisconnectTimeouts: new Map(), // deviceId -> timeoutId
  deviceDisconnectCountdowns: new Map() // deviceId -> countdown info
}

const getters = {
  connected: state => state.connected,
  socket: state => state.socket,
  websocketManager: state => state.websocketManager
}

const mutations = {
  SET_CONNECTED(state, connected) {
    state.connected = connected
  },
  SET_SOCKET(state, socket) {
    state.socket = socket
  },
  SET_WEBSOCKET_MANAGER(state, manager) {
    state.websocketManager = manager
  },

  // 设备断开连接超时相关mutations
  SET_DEVICE_DISCONNECT_COUNTDOWN(state, { deviceId, countdownInfo }) {
    state.deviceDisconnectCountdowns.set(deviceId, countdownInfo)
  },

  CLEAR_DEVICE_DISCONNECT_COUNTDOWN(state, deviceId) {
    state.deviceDisconnectCountdowns.delete(deviceId)
  }
}

const actions = {
  connect({ commit, rootGetters, rootState }, socket) {
    commit('SET_SOCKET', socket)

    socket.on('connect', () => {
      console.log('WebSocket连接成功')
      commit('SET_CONNECTED', true)

      // 注册为Web客户端
      const user = rootGetters['auth/user']
      const isAuthenticated = rootGetters['auth/isAuthenticated']

      console.log('🔍 [WebSocket] 用户认证状态检查:')
      console.log('🔍 [WebSocket] isAuthenticated:', isAuthenticated)
      console.log('🔍 [WebSocket] user:', user)

      if (isAuthenticated && user) {
        console.log('🔍 [WebSocket] 发送认证用户连接:', { userId: user.id, username: user.username })
        socket.emit('web_client_connect', {
          userId: user.id,
          username: user.username
        })
        console.log('已注册为认证Web客户端:', user.username)
      } else {
        // 未登录状态下也注册为匿名Web客户端，以便接收设备事件
        const anonymousUserId = 'anonymous_' + Date.now()
        console.log('🔍 [WebSocket] 发送匿名用户连接:', { userId: anonymousUserId, username: 'anonymous', clientType: 'anonymous_web' })
        socket.emit('web_client_connect', {
          userId: anonymousUserId,
          username: 'anonymous',
          clientType: 'anonymous_web'
        })
        console.log('已注册为匿名Web客户端')
      }
    })

    socket.on('disconnect', () => {
      console.log('WebSocket连接断开')
      commit('SET_CONNECTED', false)
    })

    socket.on('devices_list', (devices) => {
      // 更新设备列表
      commit('device/SET_DEVICES', devices, { root: true })
    })

    socket.on('device_online', (device) => {
      // 设备上线
      commit('device/ADD_DEVICE', device, { root: true })
    })

    socket.on('device_offline', ({ deviceId }) => {
      // 设备离线
      commit('device/UPDATE_DEVICE', {
        deviceId,
        updates: { status: 'offline' }
      }, { root: true })
    })

    socket.on('script_result', (result) => {
      // 脚本执行结果
      console.log('脚本执行结果:', result)
      // 可以在这里触发通知或更新UI
    })

    socket.on('xiaohongshu_execution_completed', (data) => {
      // 小红书脚本执行完成
      console.log('小红书脚本执行完成:', data)
      // 通知小红书自动化页面重置状态
      commit('xiaohongshu/RESET_EXECUTION_STATE', data, { root: true })
    })

    socket.on('xianyu_execution_completed', (data) => {
      // 闲鱼脚本执行完成
      console.log('闲鱼脚本执行完成:', data)
      // 通知闲鱼自动化页面重置状态
      commit('xianyu/RESET_EXECUTION_STATE', data, { root: true })
    })

    // 监听小红书脚本重置事件
    socket.on('xiaohongshu_script_reset', (data) => {
      console.log('[Socket] 收到小红书脚本重置事件:', data)
      // 通知小红书模块重置对应功能的状态
      commit('xiaohongshu/SET_FUNCTION_STATE', {
        functionType: data.functionType,
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          taskId: null,
          config: {},
          selectedDevices: [],
          startTime: null,
          progress: 0,
          status: 'idle',
          logs: [],
          lastResult: null
        }
      }, { root: true })
    })

    // 监听小红书Vuex状态更新事件
    socket.on('xiaohongshu_vuex_state_update', (data) => {
      console.log('[Socket] 收到小红书Vuex状态更新事件:', data)
      if (data.action === 'stopTask') {
        commit('xiaohongshu/STOP_TASK', {
          functionType: data.functionType,
          reason: data.reason || 'server_stop'
        }, { root: true })
      }
    })

    // 监听设备状态更新事件（合并重复的监听器）
    socket.on('device_status_update', (data) => {
      console.log('[Socket] 收到设备状态更新事件:', data)
      // 通知设备管理模块更新设备状态
      commit('device/UPDATE_DEVICE_STATUS', data, { root: true })
      // 同时更新设备列表中的设备状态
      commit('device/UPDATE_DEVICE', {
        deviceId: data.deviceId,
        updates: {
          status: data.status,
          last_seen: data.lastSeen || new Date()
        }
      }, { root: true })
    })

    // 监听设备状态变化事件（包含设备断开连接）
    socket.on('device_status_changed', (data) => {
      console.log('[Socket] 收到设备状态变化事件:', data)

      if (data.type === 'device_connected') {
        console.log('[Socket] 设备重新连接，取消断开连接超时处理:', data.deviceId)

        // 取消该设备的断开连接超时处理
        dispatch('cancelDeviceDisconnectTimeout', data.deviceId)

        // 更新设备状态为在线
        commit('device/UPDATE_DEVICE_STATUS', {
          deviceId: data.deviceId,
          status: 'online',
          lastSeen: data.timestamp || new Date()
        }, { root: true })

        console.log('[Socket] 设备重新连接处理完成:', data.deviceId)
      } else if (data.type === 'device_disconnected') {
        console.log('[Socket] 设备断开连接，开始清理相关状态:', data.deviceId)

        // 1. 更新设备状态为离线
        commit('device/UPDATE_DEVICE_STATUS', {
          deviceId: data.deviceId,
          status: 'offline',
          lastSeen: data.timestamp || new Date()
        }, { root: true })

        // 2. 清理小红书自动化中该设备的执行状态和选择状态
        commit('xiaohongshu/CLEAR_DEVICE_STATE', {
          deviceId: data.deviceId
        }, { root: true })

        // 3. 清理闲鱼自动化中该设备的执行状态和选择状态
        commit('xianyu/CLEAR_DEVICE_STATE', {
          deviceId: data.deviceId
        }, { root: true })

        // 4. 从设备选择列表中移除该设备
        const currentSelectedDevices = rootState.device.selectedDevices || []
        const updatedSelectedDevices = currentSelectedDevices.filter(id => id !== data.deviceId)
        if (updatedSelectedDevices.length !== currentSelectedDevices.length) {
          commit('device/SET_SELECTED_DEVICES', updatedSelectedDevices, { root: true })
        }

        // 5. 触发设备断开连接的60秒超时处理
        dispatch('startDeviceDisconnectTimeout', {
          deviceId: data.deviceId,
          deviceName: data.deviceName || '未知设备'
        }, { root: true })

        console.log('[Socket] 设备断开连接状态清理完成:', data.deviceId)
      }
    })

    // 监听设备离线事件（兼容旧版本）
    socket.on('device_offline', (data) => {
      console.log('[Socket] 收到设备离线事件:', data)

      // 触发设备断开连接的处理逻辑
      const statusChangeData = {
        type: 'device_disconnected',
        deviceId: data.deviceId,
        timestamp: new Date()
      }

      // 复用设备状态变化的处理逻辑
      socket.emit('device_status_changed', statusChangeData)
    })
  },

  disconnect({ commit, state }) {
    if (state.socket) {
      state.socket.disconnect()
      commit('SET_SOCKET', null)
      commit('SET_CONNECTED', false)
    }
  },

  // 启动设备断开连接的60秒超时处理
  startDeviceDisconnectTimeout({ commit, state }, { deviceId, deviceName }) {
    console.log(`[Socket] 启动设备断开连接超时处理: ${deviceId} (${deviceName})`)

    // 清除之前的超时处理（如果存在）
    if (state.deviceDisconnectTimeouts.has(deviceId)) {
      clearTimeout(state.deviceDisconnectTimeouts.get(deviceId))
      const countdownInfo = state.deviceDisconnectCountdowns.get(deviceId)
      if (countdownInfo?.intervalId) {
        clearInterval(countdownInfo.intervalId)
      }
    }

    // 设置60秒倒计时
    let countdown = 60
    const countdownInfo = {
      deviceId,
      deviceName,
      remainingSeconds: countdown,
      intervalId: null
    }

    // 更新倒计时显示
    const updateCountdown = () => {
      countdownInfo.remainingSeconds = countdown
      commit('SET_DEVICE_DISCONNECT_COUNTDOWN', { deviceId, countdownInfo: { ...countdownInfo } })

      if (countdown <= 0) {
        // 倒计时结束，执行超时处理
        this.dispatch('socket/handleDeviceDisconnectTimeout', deviceId, { root: true })
      } else {
        countdown--
      }
    }

    // 立即更新一次
    updateCountdown()

    // 每秒更新倒计时
    const intervalId = setInterval(updateCountdown, 1000)
    countdownInfo.intervalId = intervalId

    // 设置60秒超时
    const timeoutId = setTimeout(() => {
      this.dispatch('socket/handleDeviceDisconnectTimeout', deviceId, { root: true })
    }, 60000)

    // 保存超时和倒计时信息
    state.deviceDisconnectTimeouts.set(deviceId, timeoutId)
    state.deviceDisconnectCountdowns.set(deviceId, countdownInfo)
  },

  // 处理设备断开连接超时
  handleDeviceDisconnectTimeout({ commit, state }, deviceId) {
    console.log(`[Socket] 设备断开连接超时处理: ${deviceId}`)

    // 清理超时和倒计时
    if (state.deviceDisconnectTimeouts.has(deviceId)) {
      clearTimeout(state.deviceDisconnectTimeouts.get(deviceId))
      state.deviceDisconnectTimeouts.delete(deviceId)
    }

    if (state.deviceDisconnectCountdowns.has(deviceId)) {
      const countdownInfo = state.deviceDisconnectCountdowns.get(deviceId)
      if (countdownInfo.intervalId) {
        clearInterval(countdownInfo.intervalId)
      }
      state.deviceDisconnectCountdowns.delete(deviceId)
    }

    // 清除倒计时显示
    commit('CLEAR_DEVICE_DISCONNECT_COUNTDOWN', deviceId)

    // 通知相关模块停止该设备的脚本执行状态显示
    commit('xiaohongshu/FORCE_STOP_DEVICE_EXECUTION', { deviceId }, { root: true })
    commit('xianyu/FORCE_STOP_DEVICE_EXECUTION', { deviceId }, { root: true })

    console.log(`[Socket] 设备 ${deviceId} 断开连接超时处理完成`)
  },

  // 取消设备断开连接超时（设备重新连接时调用）
  cancelDeviceDisconnectTimeout({ commit, state }, deviceId) {
    console.log(`[Socket] 取消设备断开连接超时处理: ${deviceId}`)

    if (state.deviceDisconnectTimeouts.has(deviceId)) {
      clearTimeout(state.deviceDisconnectTimeouts.get(deviceId))
      state.deviceDisconnectTimeouts.delete(deviceId)
    }

    if (state.deviceDisconnectCountdowns.has(deviceId)) {
      const countdownInfo = state.deviceDisconnectCountdowns.get(deviceId)
      if (countdownInfo.intervalId) {
        clearInterval(countdownInfo.intervalId)
      }
      state.deviceDisconnectCountdowns.delete(deviceId)
    }

    // 清除倒计时显示
    commit('CLEAR_DEVICE_DISCONNECT_COUNTDOWN', deviceId)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
