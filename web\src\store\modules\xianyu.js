// 闲鱼自动化状态管理模块
import axios from 'axios'

// localStorage持久化工具函数
const STORAGE_KEY = 'xianyu_function_states'

const loadStateFromStorage = () => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY)
    if (saved) {
      const parsed = JSON.parse(saved)
      console.log('从localStorage恢复闲鱼状态:', parsed)
      return parsed
    }
  } catch (error) {
    console.error('从localStorage恢复闲鱼状态失败:', error)
  }
  return {}
}

const saveStateToStorage = (functionStates) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(functionStates))
    console.log('闲鱼状态已保存到localStorage')
  } catch (error) {
    console.error('保存闲鱼状态到localStorage失败:', error)
  }
}

// 默认状态工厂函数
const createDefaultFunctionState = () => ({
  isScriptRunning: false,
  isScriptCompleted: false,
  taskId: null,
  config: {},
  selectedDevices: [],
  startTime: null,
  progress: 0,
  status: 'idle',
  logs: [],
  lastResult: null,
  // 实时状态数据
  realtimeData: {}
})

// 从localStorage恢复状态
const savedStates = loadStateFromStorage()

const state = {
  // 各功能的状态数据 - 合并默认状态和保存的状态
  functionStates: {
    keywordMessage: {
      ...createDefaultFunctionState(),
      ...(savedStates.keywordMessage || {})
    }
  },
  
  // 执行日志
  executionLogs: [],
  
  // 当前执行的任务
  currentTasks: {}
}

const getters = {
  // 获取指定功能的状态
  getFunctionState: (state) => (functionType) => {
    return state.functionStates[functionType] || {}
  },
  
  // 获取指定功能是否正在运行
  isFunctionRunning: (state) => (functionType) => {
    const functionState = state.functionStates[functionType]
    return functionState ? functionState.isScriptRunning : false
  },
  
  // 获取指定功能是否已完成
  isFunctionCompleted: (state) => (functionType) => {
    const functionState = state.functionStates[functionType]
    return functionState ? functionState.isScriptCompleted : false
  },
  
  // 获取执行日志
  getExecutionLogs: (state) => state.executionLogs,
  
  // 获取当前任务
  getCurrentTasks: (state) => state.currentTasks,

  // 获取设备的任务列表
  getDeviceTasks: (state) => (deviceId) => {
    const tasks = []
    
    // 检查当前任务中是否有该设备的任务
    if (state.currentTasks[deviceId]) {
      const taskData = state.currentTasks[deviceId]
      tasks.push({
        functionType: taskData.functionType || 'keywordMessage',
        taskId: taskData.taskId,
        status: taskData.status || 'running',
        startTime: taskData.startTime,
        progress: taskData.progress || 0
      })
    }
    
    // 遍历所有功能状态，查找包含该设备的运行任务
    Object.keys(state.functionStates).forEach(functionType => {
      const functionState = state.functionStates[functionType]
      
      if (functionState.isScriptRunning) {
        // 检查当前任务中是否有该设备的任务，并且功能类型匹配
        if (state.currentTasks[deviceId] && 
            state.currentTasks[deviceId].functionType === functionType) {
          // 避免重复添加
          const existingTask = tasks.find(task => 
            task.functionType === functionType && 
            task.taskId === functionState.currentTaskId
          )
          
          if (!existingTask) {
            tasks.push({
              functionType: functionType,
              taskId: functionState.currentTaskId,
              status: 'running',
              startTime: state.currentTasks[deviceId].startTime || new Date().toISOString(),
              progress: 0
            })
          }
        }
      }
    })
    
    console.log(`[闲鱼Vuex] 设备 ${deviceId} 的任务列表:`, tasks)
    return tasks
  }
}

const mutations = {
  // 设置功能状态
  SET_FUNCTION_STATE(state, { functionType, stateData }) {
    if (!state.functionStates[functionType]) {
      state.functionStates[functionType] = createDefaultFunctionState()
    }

    // 合并状态数据
    Object.assign(state.functionStates[functionType], stateData)

    // 自动保存到localStorage
    saveStateToStorage(state.functionStates)
  },
  
  // 重置功能状态
  RESET_FUNCTION_STATE(state, functionType) {
    if (state.functionStates[functionType]) {
      state.functionStates[functionType] = createDefaultFunctionState()
    }

    // 自动保存到localStorage
    saveStateToStorage(state.functionStates)
  },
  
  // 设置脚本运行状态
  SET_SCRIPT_RUNNING(state, { functionType, isRunning, taskId = null, logId = null }) {
    if (!state.functionStates[functionType]) {
      state.functionStates[functionType] = {
        isScriptRunning: false,
        isScriptCompleted: false,
        config: {},
        currentLogId: null,
        currentTaskId: null
      }
    }
    
    state.functionStates[functionType].isScriptRunning = isRunning
    if (isRunning) {
      state.functionStates[functionType].isScriptCompleted = false
      if (taskId) state.functionStates[functionType].currentTaskId = taskId
      if (logId) state.functionStates[functionType].currentLogId = logId
    } else {
      state.functionStates[functionType].currentTaskId = null
      state.functionStates[functionType].currentLogId = null
    }
  },
  
  // 设置脚本完成状态
  SET_SCRIPT_COMPLETED(state, { functionType, isCompleted }) {
    if (!state.functionStates[functionType]) {
      state.functionStates[functionType] = {
        isScriptRunning: false,
        isScriptCompleted: false,
        config: {},
        currentLogId: null,
        currentTaskId: null
      }
    }
    
    state.functionStates[functionType].isScriptCompleted = isCompleted
    if (isCompleted) {
      state.functionStates[functionType].isScriptRunning = false
    }
  },
  
  // 添加执行日志
  ADD_EXECUTION_LOG(state, log) {
    state.executionLogs.unshift(log)
    // 保持最多100条日志
    if (state.executionLogs.length > 100) {
      state.executionLogs = state.executionLogs.slice(0, 100)
    }
  },
  
  // 设置当前任务
  SET_CURRENT_TASK(state, { deviceId, taskData }) {
    state.currentTasks[deviceId] = taskData
  },
  
  // 移除当前任务
  REMOVE_CURRENT_TASK(state, deviceId) {
    delete state.currentTasks[deviceId]
  },

  // 添加当前任务
  ADD_CURRENT_TASK(state, { taskId, task }) {
    console.log('[闲鱼Vuex] 添加当前任务:', { taskId, task })
    if (task && task.deviceId) {
      state.currentTasks[task.deviceId] = {
        taskId: taskId,
        functionType: task.functionType,
        status: task.status || 'running',
        startTime: task.startTime,
        config: task.config || {}
      }
      console.log('[闲鱼Vuex] 当前任务列表已更新:', state.currentTasks)
    }
  },

  CLEAR_ALL_CURRENT_TASKS(state) {
    state.currentTasks = {}
  },

  // 重置执行状态（脚本执行完成时调用）
  RESET_EXECUTION_STATE(state, data) {
    console.log('[闲鱼Vuex] 重置执行状态:', data)

    // 重置所有功能的执行状态
    Object.keys(state.functionStates).forEach(functionType => {
      const functionState = state.functionStates[functionType]
      if (functionState.isScriptRunning || functionState.isScriptCompleted) {
        console.log(`[闲鱼Vuex] 重置功能 ${functionType} 的执行状态`)
        functionState.isScriptRunning = false
        functionState.isScriptCompleted = false
        functionState.currentLogId = null
        functionState.currentTaskId = null
      }
    })

    // 如果有设备ID，移除该设备的当前任务
    if (data && data.deviceId) {
      delete state.currentTasks[data.deviceId]
    }
  },

  // 清理指定设备的所有状态（设备断开连接时调用）
  CLEAR_DEVICE_STATE(state, { deviceId }) {
    console.log('[闲鱼Vuex] 清理设备状态:', deviceId)

    // 遍历所有功能状态，清理包含该设备的执行状态和选择状态
    Object.keys(state.functionStates).forEach(functionType => {
      const functionState = state.functionStates[functionType]

      // 如果该功能的选中设备包含断开的设备，从列表中移除
      if (functionState.selectedDevices && functionState.selectedDevices.includes(deviceId)) {
        const index = functionState.selectedDevices.indexOf(deviceId)
        functionState.selectedDevices.splice(index, 1)
        console.log(`[闲鱼Vuex] 从功能 ${functionType} 的选中设备中移除: ${deviceId}`)
      }

      // 如果该功能正在运行且只涉及到该设备，重置功能状态
      if (functionState.isScriptRunning &&
          functionState.selectedDevices &&
          functionState.selectedDevices.length === 0) {
        console.log(`[闲鱼Vuex] 重置功能 ${functionType} 的执行状态（设备断开）`)
        functionState.isScriptRunning = false
        functionState.isScriptCompleted = false
        functionState.taskId = null
        functionState.config = {}
        functionState.startTime = null
        functionState.progress = 0
        functionState.status = 'idle'
        functionState.logs = []
        functionState.lastResult = null
        functionState.realtimeData = {}
      }
    })

    // 清理当前任务中涉及该设备的任务
    if (state.currentTasks && state.currentTasks[deviceId]) {
      delete state.currentTasks[deviceId]
      console.log(`[闲鱼Vuex] 清理设备 ${deviceId} 的当前任务`)
    }

    // 自动保存到localStorage
    saveStateToStorage(state.functionStates)
  },

  // 强制停止指定设备的脚本执行状态显示（设备断开连接超时时调用）
  FORCE_STOP_DEVICE_EXECUTION(state, { deviceId }) {
    console.log('[闲鱼Vuex] 强制停止设备执行状态显示:', deviceId)

    // 遍历所有功能状态，强制停止涉及该设备的执行状态
    Object.keys(state.functionStates).forEach(functionType => {
      const functionState = state.functionStates[functionType]

      // 如果该功能正在运行且涉及到该设备，强制停止执行状态
      if (functionState.isScriptRunning) {
        console.log(`[闲鱼Vuex] 强制停止功能 ${functionType} 的执行状态（设备断开连接超时）`)
        functionState.isScriptRunning = false
        functionState.isScriptCompleted = false
        functionState.taskId = null
        functionState.progress = 0
        functionState.status = 'idle'
        functionState.logs = []
        functionState.lastResult = null
        functionState.realtimeData = {}
      }
    })

    // 清理当前任务中涉及该设备的任务
    if (state.currentTasks && state.currentTasks[deviceId]) {
      delete state.currentTasks[deviceId]
      console.log(`[闲鱼Vuex] 清理设备 ${deviceId} 的当前任务（强制停止）`)
    }

    // 自动保存到localStorage
    saveStateToStorage(state.functionStates)
  }
}

const actions = {
  // 设置功能状态
  setFunctionState({ commit }, { functionType, stateData }) {
    commit('SET_FUNCTION_STATE', { functionType, stateData })
  },
  
  // 重置功能状态
  resetFunctionState({ commit }, functionType) {
    commit('RESET_FUNCTION_STATE', functionType)
  },
  
  // 设置脚本运行状态
  setScriptRunning({ commit }, { functionType, isRunning, taskId, logId }) {
    commit('SET_SCRIPT_RUNNING', { functionType, isRunning, taskId, logId })
  },
  
  // 设置脚本完成状态
  setScriptCompleted({ commit }, { functionType, isCompleted }) {
    commit('SET_SCRIPT_COMPLETED', { functionType, isCompleted })
  },
  
  // 任务开始
  taskStarted({ commit }, { functionType, taskId, logId, config, deviceId }) {
    commit('SET_SCRIPT_RUNNING', { 
      functionType, 
      isRunning: true, 
      taskId, 
      logId 
    })
    
    // 如果有配置，也保存配置
    if (config) {
      commit('SET_FUNCTION_STATE', { 
        functionType, 
        stateData: { config } 
      })
    }

    // 如果有设备ID，添加到当前任务列表
    if (deviceId) {
      commit('SET_CURRENT_TASK', {
        deviceId: deviceId,
        taskData: {
          functionType: functionType,
          taskId: taskId,
          status: 'running',
          startTime: new Date().toISOString(),
          config: config || {}
        }
      })
    }
  },
  
  // 任务停止
  taskStopped({ commit }, { functionType }) {
    commit('SET_SCRIPT_RUNNING', { 
      functionType, 
      isRunning: false 
    })
  },
  
  // 任务完成
  taskCompleted({ commit }, { functionType }) {
    commit('SET_SCRIPT_COMPLETED', { 
      functionType, 
      isCompleted: true 
    })
    
    // 1分钟后重置完成状态
    setTimeout(() => {
      commit('SET_SCRIPT_COMPLETED', { 
        functionType, 
        isCompleted: false 
      })
    }, 60000)
  },
  
  // 添加执行日志
  addExecutionLog({ commit }, log) {
    const logEntry = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      ...log
    }
    commit('ADD_EXECUTION_LOG', logEntry)
  },

  // 清理指定设备的执行状态
  clearDeviceExecutionState({ commit, state }, deviceId) {
    console.log(`[闲鱼Vuex] 清理设备 ${deviceId} 的执行状态`)

    // 遍历所有功能状态，清理包含该设备的执行状态
    Object.keys(state.functionStates).forEach(functionType => {
      const functionState = state.functionStates[functionType]

      // 如果该功能正在运行且涉及到该设备，重置功能状态
      if (functionState.isScriptRunning) {
        console.log(`[闲鱼Vuex] 重置功能 ${functionType} 的执行状态`)

        commit('SET_FUNCTION_STATE', {
          functionType,
          stateData: {
            isScriptRunning: false,
            isScriptCompleted: false,
            config: {},
            currentLogId: null,
            currentTaskId: null
          }
        })
      }
    })

    // 清理当前任务中涉及该设备的任务
    // 直接移除该设备的任务（currentTasks以deviceId为键）
    if (state.currentTasks[deviceId]) {
      console.log(`[闲鱼Vuex] 清理设备 ${deviceId} 的任务`)
      commit('REMOVE_CURRENT_TASK', deviceId)
    }
  },

  // 恢复执行状态（页面刷新时调用）
  async restoreExecutionState({ commit, rootGetters }) {
    try {
      console.log('[闲鱼Vuex] 开始恢复执行状态...')

      // 获取正在执行的闲鱼任务 - 使用axios确保认证头正确传递
      const response = await axios.get('/api/xianyu/logs', {
        params: {
          page: 1,
          limit: 50,
          executionStatus: 'running'
        }
      })
      const result = response.data

      console.log('[闲鱼Vuex] API返回的完整数据:', result)

      // 处理API返回的数据结构：result.data.logs 或 result.logs
      const logs = result.data?.logs || result.logs || []

      if (result.success && logs && logs.length > 0) {
        console.log(`[闲鱼Vuex] 发现 ${logs.length} 个正在执行的任务`)
        console.log('[闲鱼Vuex] 任务详情:', logs)

        // 详细输出每个任务的结构
        logs.forEach((log, index) => {
          console.log(`[闲鱼Vuex] 任务 ${index + 1} 结构:`, {
            id: log.id,
            functionType: log.functionType,
            deviceInfo: log.deviceInfo,
            device_id: log.device_id,
            deviceId: log.deviceId,
            started_at: log.started_at,
            config_params: log.config_params
          })
        })

        // 按功能类型分组
        const functionGroups = {}
        logs.forEach(log => {
          const functionType = log.functionType || 'keywordMessage'
          if (!functionGroups[functionType]) {
            functionGroups[functionType] = []
          }
          functionGroups[functionType].push(log)
        })

        // 恢复各功能的状态
        Object.keys(functionGroups).forEach(functionType => {
          const tasks = functionGroups[functionType]
          const latestTask = tasks[0] // 取最新的任务

          console.log(`[闲鱼Vuex] 恢复功能 ${functionType} 的运行状态:`, latestTask)

          // 解析配置参数
          let configParams = {}
          try {
            if (latestTask.configParams) {
              configParams = typeof latestTask.configParams === 'string'
                ? JSON.parse(latestTask.configParams)
                : latestTask.configParams
            } else if (latestTask.config_params) {
              configParams = typeof latestTask.config_params === 'string'
                ? JSON.parse(latestTask.config_params)
                : latestTask.config_params
            }
          } catch (e) {
            console.warn(`[闲鱼Vuex] 解析配置参数失败:`, e)
            configParams = {}
          }

          commit('SET_FUNCTION_STATE', {
            functionType,
            stateData: {
              isScriptRunning: true,
              isScriptCompleted: false,
              currentLogId: latestTask.id,
              currentTaskId: latestTask.id,
              config: configParams
            }
          })

          console.log(`[闲鱼Vuex] 功能 ${functionType} 状态已恢复为运行中`)
          console.log(`[闲鱼Vuex] 恢复的配置参数:`, configParams)

          // 添加到当前任务列表
          console.log(`[闲鱼Vuex] 准备添加任务到当前任务列表:`, latestTask)

          // 尝试多种可能的设备ID字段
          let deviceId = latestTask.deviceInfo?.id ||
                        latestTask.deviceInfo?.device_id ||
                        latestTask.device_id ||
                        latestTask.deviceId

          console.log(`[闲鱼Vuex] 提取的设备ID: ${deviceId}`)

          if (deviceId) {
            commit('ADD_CURRENT_TASK', {
              taskId: latestTask.id,
              task: {
                deviceId: deviceId,
                functionType: functionType,
                status: 'running',
                startTime: latestTask.started_at,
                config: latestTask.config_params || {}
              }
            })
          } else {
            console.error('[闲鱼Vuex] 无法提取设备ID，任务数据:', latestTask)
          }
        })

        console.log('[闲鱼Vuex] 任务状态恢复完成')
      } else {
        console.log('[闲鱼Vuex] 没有发现运行中的任务')
        console.log('[闲鱼Vuex] 检查条件: success =', result.success, ', logs =', logs, ', length =', logs ? logs.length : 'undefined')
      }
    } catch (error) {
      console.error('[闲鱼Vuex] 恢复执行状态失败:', error)
    }
  },

  // 重置所有状态
  resetAllStates({ commit }) {
    console.log('[闲鱼Vuex] 重置所有状态')

    // 重置所有执行状态
    commit('RESET_EXECUTION_STATE', 'keywordMessage')

    // 清空当前任务列表
    commit('CLEAR_ALL_CURRENT_TASKS')

    console.log('[闲鱼Vuex] 所有状态已重置')
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
