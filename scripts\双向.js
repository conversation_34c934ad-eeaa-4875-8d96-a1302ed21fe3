"ui";

var connected = false;
var serverUrl = "http://************:3002"; // 默认服务器地址
var token = "";
var deviceId = "";
var deviceIP = "";
var connectionCode = ""; // 设备连接码（可选）
var currentScriptThread = null; // 当前执行的脚本线程
var currentTaskId = null; // 当前任务ID
var currentScriptContent = null; // 当前执行的脚本内容
var serverCheckInterval = null; // 服务器连接检查定时器
var isServerAlive = true; // 服务器是否存活

// 发送脚本状态更新到服务器
function sendStatusUpdate(stage, status, progress, message, debugInfo) {
    if (!connected || !deviceId) {
        return;
    }

    try {
        var statusData = {
            deviceId: deviceId,
            status: status,
            progress: progress || 0,
            message: message || "",
            stage: stage,
            taskId: currentTaskId,
            timestamp: new Date().toISOString(),
            debugInfo: debugInfo || null
        };

        // 检测任务类型，决定发送到哪个API
        var apiEndpoint = "/api/xiaohongshu/status"; // 默认小红书API

        // 检查当前执行的脚本内容来判断任务类型
        if (typeof currentScriptContent !== 'undefined' && currentScriptContent) {
            if (currentScriptContent.includes('闲鱼') ||
                currentScriptContent.includes('xianyu') ||
                currentScriptContent.includes('关键词私信') ||
                currentScriptContent.includes('搜索商品')) {
                apiEndpoint = "/api/xianyu/status";
                log("检测到闲鱼任务，发送状态到闲鱼API");
            }
        }

        // 使用线程发送状态更新，避免阻塞主线程
        threads.start(function() {
            try {
                var response = http.postJson(serverUrl + apiEndpoint, statusData, {
                    headers: {
                        "Content-Type": "application/json"
                    },
                    timeout: 3000
                });

                if (response && response.statusCode === 200) {
                    log("状态更新发送成功: " + stage + " - " + message + " (API: " + apiEndpoint + ")");
                } else {
                    log("状态更新发送失败: " + (response ? response.statusCode : "无响应") + " (API: " + apiEndpoint + ")");
                }
            } catch (e) {
                log("状态更新网络错误: " + e.message + " (API: " + apiEndpoint + ")");
            }
        });

    } catch (e) {
        log("状态更新错误: " + e.message);
    }
}

// 发送调试信息到服务器
function sendDebugLog(message, level) {
    if (!connected || !deviceId) {
        return;
    }

    level = level || 'info';

    try {
        var logData = {
            deviceId: deviceId,
            taskId: currentTaskId,
            message: message,
            level: level,
            timestamp: new Date().toISOString()
        };

        // 使用线程发送调试日志，避免阻塞主线程
        threads.start(function() {
            try {
                var response = http.postJson(serverUrl + "/api/xiaohongshu/debug-log", logData, {
                    headers: {
                        "Content-Type": "application/json"
                    },
                    timeout: 2000
                });

                // 调试日志发送不需要记录成功信息，避免日志循环
            } catch (e) {
                // 静默处理调试日志发送错误
            }
        });

    } catch (e) {
        // 静默处理调试日志错误
    }
}

// 重写log函数，同时发送到服务器
var originalLog = log;
function log(message) {
    originalLog(message);
    sendDebugLog(message, 'info');
}

ui.layout(
    <vertical padding="16" bg="#F5F5F5">
        <text text="双向断开通信测试" textSize="22sp" gravity="center" textColor="#2C3E50" margin="0 0 16 0"/>

        <card margin="8" cardCornerRadius="12dp" cardElevation="4dp">
            <vertical padding="16">
                <text text="服务器配置" textSize="16sp" textColor="#34495E" margin="0 0 8 0"/>
                <horizontal margin="0 0 8 0">
                    <text text="服务器地址:" textSize="14sp" textColor="#666666" layout_weight="0" margin="0 8 0 0"/>
                    <input id="serverInput" text="************:3002" textSize="14sp" layout_weight="1" hint="输入服务器地址"/>
                </horizontal>
                <horizontal margin="0 0 8 0">
                    <text text="连接码:" textSize="14sp" textColor="#666666" layout_weight="0" margin="0 8 0 0"/>
                    <input id="connectionCodeInput" text="" textSize="14sp" layout_weight="1" hint="输入8位连接码（新设备必需）"/>
                </horizontal>
                <text id="status" text="未连接" textSize="14sp" textColor="#E74C3C" margin="8 0 0 0"/>
                <text id="deviceInfo" text="设备ID: 未生成" textSize="12sp" textColor="#7F8C8D" margin="4 0 0 0"/>
                <text id="ipInfo" text="设备IP: 获取中..." textSize="12sp" textColor="#7F8C8D" margin="4 0 0 0"/>
                <text id="serverStatus" text="服务器状态: 未检测" textSize="12sp" textColor="#7F8C8D" margin="4 0 0 0"/>
            </vertical>
        </card>

        <horizontal margin="8">
            <button id="connect" text="连接" layout_weight="1" style="Widget.AppCompat.Button.Colored" margin="0 4 0 0"/>
            <button id="disconnect" text="断开" layout_weight="1" enabled="false" margin="0 0 0 4"/>
        </horizontal>

        <card margin="8" cardCornerRadius="12dp" cardElevation="4dp">
            <vertical padding="16">
                <text text="双向断开测试说明" textSize="16sp" textColor="#34495E" margin="0 0 8 0"/>
                <text text="1. 连接后，在PC端点击断开，手机端会收到通知" textSize="12sp" textColor="#666666" margin="4 0"/>
                <text text="2. 在手机端点击断开，PC端会实时更新状态" textSize="12sp" textColor="#666666" margin="4 0"/>
                <text text="3. 实现真正的双向实时通信" textSize="12sp" textColor="#666666" margin="4 0"/>
            </vertical>
        </card>

        <card margin="8" cardCornerRadius="12dp" cardElevation="4dp">
            <vertical padding="16">
                <horizontal>
                    <text text="实时日志" textSize="16sp" textColor="#34495E" layout_weight="1"/>
                    <button id="clear" text="清空" textSize="12sp" style="Widget.AppCompat.Button.Borderless"/>
                </horizontal>
                <ScrollView layout_height="250dp" margin="8 0 0 0">
                    <text id="log" text="双向断开通信测试已启动..." textSize="12sp" textColor="#2C3E50"/>
                </ScrollView>
            </vertical>
        </card>
    </vertical>
);

function log(msg) {
    try {
        var time = new Date().toTimeString().substring(0, 8);
        var text = "[" + time + "] " + msg;

        ui.run(function() {
            try {
                // 检查UI元素是否存在
                if (ui.log && typeof ui.log.text === 'function') {
                    var current = ui.log.text();
                    var lines = current.split("\n");

                    // 严格限制日志行数，防止内存溢出
                    if (lines.length >= 10) {
                        lines = lines.slice(-9);
                    }

                    lines.push(text);
                    ui.log.setText(lines.join("\n"));
                } else {
                    // UI元素不存在时，只在控制台输出
                    console.log("UI元素不可用: " + text);
                }
            } catch (e) {
                // UI更新失败时静默处理，避免错误循环
                console.log("UI更新失败: " + e.message);
            }
        });

        console.log(text);

    } catch (e) {
        // 日志记录失败时静默处理
        console.log("日志记录失败: " + e.message);
    }
}



function getDeviceInfo() {
    try {
        // 获取IP地址
        var wifiManager = context.getSystemService(context.WIFI_SERVICE);
        var wifiInfo = wifiManager.getConnectionInfo();
        var ipAddress = wifiInfo.getIpAddress();

        if (ipAddress != 0) {
            deviceIP = ((ipAddress & 0xff) + "." + (ipAddress >> 8 & 0xff) + "." +
                       (ipAddress >> 16 & 0xff) + "." + (ipAddress >> 24 & 0xff));
            log("设备IP: " + deviceIP);
        } else {
            deviceIP = "未知";
            log("无法获取设备IP地址");
        }

        // 获取屏幕信息
        var screenWidth = 1080;
        var screenHeight = 1920;
        try {
            screenWidth = context.getResources().getDisplayMetrics().widthPixels;
            screenHeight = context.getResources().getDisplayMetrics().heightPixels;
        } catch (e) {
            log("获取屏幕分辨率失败，使用默认值: " + e.message);
        }
        log("屏幕分辨率: " + screenWidth + "x" + screenHeight);

        // 获取设备基本信息
        var deviceBrand = "未知";
        var deviceModel = "未知";
        var androidVersion = "未知";
        var sdkVersion = "未知";

        try {
            deviceBrand = android.os.Build.BRAND || "未知";
            deviceModel = android.os.Build.MODEL || "未知";
            androidVersion = android.os.Build.VERSION.RELEASE || "未知";
            sdkVersion = android.os.Build.VERSION.SDK_INT || "未知";
        } catch (e) {
            log("获取设备基本信息失败: " + e.message);
        }

        log("设备品牌: " + deviceBrand);
        log("设备型号: " + deviceModel);
        log("Android版本: " + androidVersion);
        log("SDK版本: " + sdkVersion);

        // 获取系统信息
        var totalMemory = "未知";
        var availableMemory = "未知";
        try {
            var activityManager = context.getSystemService(context.ACTIVITY_SERVICE);
            var memInfo = new android.app.ActivityManager.MemoryInfo();
            activityManager.getMemoryInfo(memInfo);
            totalMemory = Math.round(memInfo.totalMem / 1024 / 1024) + "MB";
            availableMemory = Math.round(memInfo.availMem / 1024 / 1024) + "MB";
            log("总内存: " + totalMemory);
            log("可用内存: " + availableMemory);
        } catch (e) {
            log("获取内存信息失败: " + e.message);
        }

        // 获取电池信息
        var batteryLevel = "未知";
        var batteryStatus = "未知";
        try {
            var batteryManager = context.getSystemService(context.BATTERY_SERVICE);
            if (batteryManager) {
                batteryLevel = batteryManager.getIntProperty(android.os.BatteryManager.BATTERY_PROPERTY_CAPACITY) + "%";
                log("电池电量: " + batteryLevel);
            }
        } catch (e) {
            log("获取电池信息失败: " + e.message);
        }

        // 获取存储信息
        var storageInfo = "未知";
        try {
            var statFs = new android.os.StatFs(android.os.Environment.getDataDirectory().getPath());
            var totalBytes = statFs.getTotalBytes();
            var availableBytes = statFs.getAvailableBytes();
            var totalGB = Math.round(totalBytes / 1024 / 1024 / 1024 * 100) / 100;
            var availableGB = Math.round(availableBytes / 1024 / 1024 / 1024 * 100) / 100;
            storageInfo = "总存储: " + totalGB + "GB, 可用: " + availableGB + "GB";
            log(storageInfo);
        } catch (e) {
            log("获取存储信息失败: " + e.message);
        }

        // 更新UI显示
        ui.run(function() {
            try {
                if (ui.ipInfo && typeof ui.ipInfo.setText === 'function') {
                    ui.ipInfo.setText("设备IP: " + deviceIP + " | 分辨率: " + screenWidth + "x" + screenHeight);
                }
            } catch (e) {
                console.log("更新IP信息UI失败: " + e.message);
            }
        });

        // 返回完整的设备信息对象
        return {
            ipAddress: deviceIP,
            screenWidth: screenWidth,
            screenHeight: screenHeight,
            brand: deviceBrand,
            model: deviceModel,
            androidVersion: androidVersion,
            sdkVersion: sdkVersion,
            totalMemory: totalMemory,
            availableMemory: availableMemory,
            batteryLevel: batteryLevel,
            batteryStatus: batteryStatus,
            storageInfo: storageInfo,
            autoJsVersion: "Auto.js",
            deviceTime: new Date().toISOString()
        };

    } catch (e) {
        deviceIP = "获取失败";
        log("获取设备信息失败: " + e.message);
        ui.run(function() {
            try {
                if (ui.ipInfo && typeof ui.ipInfo.setText === 'function') {
                    ui.ipInfo.setText("设备IP: 获取失败");
                }
            } catch (uiError) {
                console.log("更新IP信息UI失败: " + uiError.message);
            }
        });
        return null;
    }
}

function connectToServer() {
    // 从输入框获取服务器地址
    var inputAddress = ui.serverInput.getText().toString().trim();
    if (inputAddress) {
        serverUrl = "http://" + inputAddress;
    }

    // 先获取设备信息以获取IP地址
    log("正在获取设备详细信息...");
    var deviceInfo = getDeviceInfo();

    // 基于IP地址生成固定的设备ID，确保同一设备重连时ID保持一致
    var ipBasedId = deviceInfo && deviceInfo.ipAddress && deviceInfo.ipAddress !== "未知"
        ? deviceInfo.ipAddress.replace(/\./g, "_")
        : "unknown_" + Date.now();
    deviceId = "device_" + ipBasedId;

    log("=== 开始双向通信测试 ===");
    log("服务器: " + serverUrl);
    log("设备ID: " + deviceId);
    log("设备IP: " + (deviceInfo ? deviceInfo.ipAddress : "未知"));

    ui.run(function() {
        try {
            if (ui.deviceInfo && typeof ui.deviceInfo.setText === 'function') {
                ui.deviceInfo.setText("设备ID: " + deviceId);
            }
        } catch (e) {
            console.log("更新设备ID UI失败: " + e.message);
        }
    });

    threads.start(function() {
        try {
            // 获取连接码
            connectionCode = ui.connectionCodeInput.text().trim();

            if (connectionCode) {
                // 使用连接码连接
                log("步骤1: 使用连接码连接");
                registerDeviceWithCode(deviceInfo);
            } else {
                // 检查是否为已注册设备（尝试重连）
                log("步骤1: 尝试设备重连（无连接码）");
                registerDeviceReconnect(deviceInfo);
            }

        } catch (e) {
            log("步骤1: 连接错误: " + e.message);
        }
    });
}

// 使用连接码注册设备
function registerDeviceWithCode(detailedDeviceInfo) {
    try {
        log("步骤2: 使用连接码注册设备");

        // 使用获取到的详细设备信息，如果获取失败则使用默认值
        var finalDeviceInfo = detailedDeviceInfo || {
            ipAddress: deviceIP || "未知",
            screenWidth: 1080,
            screenHeight: 1920,
            brand: "未知",
            model: "未知",
            androidVersion: "未知",
            sdkVersion: "未知",
            totalMemory: "未知",
            availableMemory: "未知",
            batteryLevel: "未知",
            storageInfo: "未知",
            autoJsVersion: "未知",
            deviceTime: new Date().toISOString()
        };

        // 生成动态设备名称：设备型号 + IP地址
        var deviceModel = finalDeviceInfo.model || "未知";
        var deviceIP_short = (finalDeviceInfo.ipAddress || "未知IP").replace(/\./g, "");
        var dynamicDeviceName = deviceModel + "_" + deviceIP_short;

        // 如果设备型号是"未知"，使用品牌信息
        if (deviceModel === "未知") {
            var deviceBrand = finalDeviceInfo.brand || "未知";
            if (deviceBrand !== "未知") {
                dynamicDeviceName = deviceBrand + "_" + deviceIP_short;
            } else {
                dynamicDeviceName = "AutoJS设备_" + deviceIP_short;
            }
        }

        var deviceData = {
            deviceId: deviceId,
            deviceName: dynamicDeviceName,
            deviceIP: finalDeviceInfo.ipAddress,
            deviceInfo: finalDeviceInfo,
            connectionCode: connectionCode
        };

        log("使用连接码: " + connectionCode);
        log("发送设备详细信息:");
        log("- 屏幕分辨率: " + finalDeviceInfo.screenWidth + "x" + finalDeviceInfo.screenHeight);
        log("- 设备品牌: " + finalDeviceInfo.brand);
        log("- 设备型号: " + finalDeviceInfo.model);
        log("- Android版本: " + finalDeviceInfo.androidVersion);

        var registerResponse = http.postJson(serverUrl + "/api/device/register", deviceData, {
            timeout: 10000
        });

        handleRegistrationResponse(registerResponse, "连接码");

    } catch (e) {
        log("步骤2: 注册错误: " + e.message);
        ui.run(function() {
            try {
                ui.status.setText("连接失败: " + e.message);
                ui.status.setTextColor(colors.parseColor("#E74C3C"));
            } catch (e2) {
                console.log("更新UI状态失败: " + e2.message);
            }
        });
    }
}

// 设备重连（无连接码）
function registerDeviceReconnect(detailedDeviceInfo) {
    try {
        log("步骤2: 尝试设备重连");

        // 使用获取到的详细设备信息，如果获取失败则使用默认值
        var finalDeviceInfo = detailedDeviceInfo || {
            ipAddress: deviceIP || "未知",
            screenWidth: 1080,
            screenHeight: 1920,
            brand: "未知",
            model: "未知",
            androidVersion: "未知",
            sdkVersion: "未知",
            totalMemory: "未知",
            availableMemory: "未知",
            batteryLevel: "未知",
            storageInfo: "未知",
            autoJsVersion: "未知",
            deviceTime: new Date().toISOString()
        };

        // 生成动态设备名称：设备型号 + IP地址
        var deviceModel = finalDeviceInfo.model || "未知";
        var deviceIP_short = (finalDeviceInfo.ipAddress || "未知IP").replace(/\./g, "");
        var dynamicDeviceName = deviceModel + "_" + deviceIP_short;

        // 如果设备型号是"未知"，使用品牌信息
        if (deviceModel === "未知") {
            var deviceBrand = finalDeviceInfo.brand || "未知";
            if (deviceBrand !== "未知") {
                dynamicDeviceName = deviceBrand + "_" + deviceIP_short;
            } else {
                dynamicDeviceName = "AutoJS设备_" + deviceIP_short;
            }
        }

        var deviceData = {
            deviceId: deviceId,
            deviceName: dynamicDeviceName,
            deviceIP: finalDeviceInfo.ipAddress,
            deviceInfo: finalDeviceInfo
            // 注意：不提供connectionCode
        };

        log("尝试重连设备ID: " + deviceId);
        log("设备IP: " + finalDeviceInfo.ipAddress);

        var registerResponse = http.postJson(serverUrl + "/api/device/register", deviceData, {
            timeout: 10000
        });

        handleRegistrationResponse(registerResponse, "重连");

    } catch (e) {
        log("步骤2: 重连错误: " + e.message);
        ui.run(function() {
            try {
                ui.status.setText("重连失败: " + e.message);
                ui.status.setTextColor(colors.parseColor("#E74C3C"));
            } catch (e2) {
                console.log("更新UI状态失败: " + e2.message);
            }
        });
    }
}

// 处理注册响应的通用函数
function handleRegistrationResponse(registerResponse, connectionType) {
    if (registerResponse && registerResponse.body) {
        var registerResult = registerResponse.body.json();
        if (registerResult.success) {
            log("步骤2: 设备注册成功 (" + connectionType + ")");
            connected = true;

            // 从注册响应中获取设备ID
            if (registerResult.data && registerResult.data.deviceId) {
                deviceId = registerResult.data.deviceId;
                log("设备ID已设置: " + deviceId);
            } else {
                log("警告: 注册响应中未包含设备ID");
            }

            ui.run(function() {
                try {
                    ui.status.setText("已连接 (" + connectionType + ")");
                    ui.status.setTextColor(colors.parseColor("#27AE60"));
                    ui.connect.setEnabled(false);
                    ui.disconnect.setEnabled(true);
                } catch (e) {
                    console.log("更新UI状态失败: " + e.message);
                }
            });

            // 开始轮询命令
            log("步骤3: 开始轮询PC端命令...");
            startPolling();

            log("步骤4: 开始服务器状态监控...");
            startServerStatusCheck();

            log("步骤5: 开始检测设备应用...");
            detectDeviceApps();

            toast("设备已连接，可测试双向断开");
        } else {
            log("步骤2: 设备注册失败: " + registerResult.message);

            // 如果是重连失败且提示需要连接码，给出友好提示
            var errorMessage = registerResult.message;
            if (connectionType === "重连" && errorMessage.indexOf("连接码") !== -1) {
                errorMessage = "设备未注册，请输入连接码进行首次连接";
            }

            ui.run(function() {
                try {
                    ui.status.setText("连接失败: " + errorMessage);
                    ui.status.setTextColor(colors.parseColor("#E74C3C"));
                } catch (e) {
                    console.log("更新UI状态失败: " + e.message);
                }
            });
        }
    } else {
        log("步骤2: 注册响应为空");
        ui.run(function() {
            try {
                ui.status.setText("连接失败: 服务器无响应");
                ui.status.setTextColor(colors.parseColor("#E74C3C"));
            } catch (e) {
                console.log("更新UI状态失败: " + e.message);
            }
        });
    }
}



// 内存安全的轮询机制
var pollingInterval = null;
var isPolling = false;
var isScriptExecutionPolling = false; // 脚本执行期间的轮询模式

function startPolling() {
    if (!connected || isPolling || isScriptExecutionPolling) {
        return;
    }

    log("开始轮询PC端命令...");
    isPolling = true;

    // 使用setInterval而不是递归setTimeout，避免栈溢出
    pollingInterval = setInterval(function() {
        if (!connected) {
            stopPolling();
            return;
        }

        // 使用单一线程处理轮询，避免线程泄漏
        threads.start(function() {
            try {
                var commandResponse = http.get(serverUrl + "/api/device/" + deviceId + "/commands", {
                    headers: {
                        "Content-Type": "application/json"
                    },
                    timeout: 3000 // 减少超时时间
                });

                if (commandResponse.statusCode == 200) {
                    var commandResult = commandResponse.body.json();

                    if (commandResult.success && commandResult.data) {
                        var command = commandResult.data;
                        log("收到命令: " + JSON.stringify(command));

                        if (command.script === 'DISCONNECT_COMMAND' || command.type === 'disconnect') {
                            log("*** 收到PC端断开命令! ***");
                            log("PC端请求断开连接");
                            toast("PC端已断开此设备连接");
                            handlePCDisconnectCommand();
                            return;
                        } else if (command.script === 'STOP_SCRIPT_COMMAND' || command.type === 'stop_script') {
                            log("*** 收到PC端停止脚本命令! ***");
                            log("PC端请求停止当前执行的脚本");
                            toast("PC端已停止当前脚本");
                            handleStopScriptCommand(command);
                            // 不要return，继续轮询以接收新的命令
                        } else if (command.type === 'clearChatRecords') {
                            log("*** 收到清空私聊记录命令! ***");
                            log("PC端请求清空闲鱼私聊记录文件");
                            handleClearChatRecordsCommand(command);
                            // 不要return，继续轮询以接收新的命令
                        } else if (command.type === 'script_execution_completed') {
                            log("*** 收到脚本执行完成通知! ***");
                            log("服务器通知脚本已完成，停止上报忙碌状态");
                            handleScriptCompletedNotification(command);
                            // 不要return，继续轮询以接收新的命令
                        } else if (command.type === 'video_transfer') {
                            log("*** 收到视频传输命令! ***");
                            log("PC端请求传输视频文件到设备");
                            handleVideoTransferCommand(command);
                            // 不要return，继续轮询以接收新的命令
                        } else if (command.type === 'close_xiaohongshu_app') {
                            log("*** 收到关闭小红书应用命令! ***");
                            log("PC端请求关闭小红书应用，原因: " + (command.reason || "未知"));
                            handleCloseXiaohongshuAppCommand(command);
                            // 不要return，继续轮询以接收新的命令
                        } else if (command.type === 'token_update') {
                            log("*** 收到Token更新命令! ***");
                            log("注意：当前系统使用连接码模式，Token更新命令已废弃");
                            // handleTokenUpdateCommand(command); // 已废弃
                            // 不要return，继续轮询以接收新的命令
                        } else if (command.script) {
                            log("收到脚本命令: " + (command.script.length > 30 ? command.script.substring(0, 30) + "..." : command.script));
                            // 提取任务ID
                            currentTaskId = command.logId || command.taskId || null;
                            log("设置当前任务ID: " + currentTaskId);
                            log("开始执行脚本，实时日志已启用");

                            // 不完全停止轮询，而是切换到脚本执行模式的轮询
                            log("脚本开始执行，切换到脚本执行模式轮询");
                            switchToScriptExecutionPolling();

                            executeScript(command.script, command.logId);
                        } else {
                            log("收到无效的脚本命令: " + JSON.stringify(command));
                        }
                    }
                    // 无命令时不输出日志，减少噪音
                } else if (commandResponse.statusCode == 410) {
                    // 设备已离线，停止轮询
                    var errorResult = commandResponse.body.json();
                    log("⚠️ 服务器检测到设备已离线: " + (errorResult.message || "设备已离线"));
                    log("停止命令轮询，设备需要重新连接");
                    stopPolling();
                    connected = false;

                    // 更新UI状态为断开
                    ui.run(function() {
                        try {
                            if (ui.status && typeof ui.status.setText === 'function') {
                                ui.status.setText("已断开 (设备离线)");
                                ui.status.setTextColor(colors.parseColor("#E74C3C"));
                            }
                            if (ui.connect && typeof ui.connect.setEnabled === 'function') {
                                ui.connect.setEnabled(true);
                            }
                            if (ui.disconnect && typeof ui.disconnect.setEnabled === 'function') {
                                ui.disconnect.setEnabled(false);
                            }
                        } catch (e) {
                            console.log("更新离线状态UI失败: " + e.message);
                        }
                    });

                    return;
                }
                // 轮询失败时不输出日志，减少噪音

            } catch (e) {
                // 轮询错误时不输出日志，减少噪音
                // 只在连续错误时记录
            }
        });
    }, 3000); // 增加轮询间隔到3秒，减少服务器压力
}

function stopPolling() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
    }
    isPolling = false;
    isScriptExecutionPolling = false;
    log("轮询已停止");
}

// 切换到脚本执行模式的轮询（只处理停止命令和心跳）
function switchToScriptExecutionPolling() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
    }
    isPolling = false;
    isScriptExecutionPolling = true;

    log("切换到脚本执行模式轮询（只处理停止命令）");

    // 使用更长的轮询间隔，只处理停止命令
    pollingInterval = setInterval(function() {
        if (!connected) {
            stopPolling();
            return;
        }

        // 使用单一线程处理轮询
        threads.start(function() {
            try {
                var commandResponse = http.get(serverUrl + "/api/device/" + deviceId + "/commands", {
                    headers: {
                        "Content-Type": "application/json"
                    },
                    timeout: 3000
                });

                if (commandResponse.statusCode == 200) {
                    var commandResult = commandResponse.body.json();

                    if (commandResult.success && commandResult.data) {
                        var command = commandResult.data;

                        // 在脚本执行期间，只处理停止命令和断开命令
                        if (command.script === 'DISCONNECT_COMMAND' || command.type === 'disconnect') {
                            log("*** 脚本执行期间收到PC端断开命令! ***");
                            log("PC端请求断开连接");
                            toast("PC端已断开此设备连接");
                            handlePCDisconnectCommand();
                            return;
                        } else if (command.script === 'STOP_SCRIPT_COMMAND' || command.type === 'stop_script') {
                            log("*** 脚本执行期间收到PC端停止脚本命令! ***");
                            log("PC端请求停止当前执行的脚本");
                            toast("PC端已停止当前脚本");
                            handleStopScriptCommand(command);
                            return;
                        }
                        // 忽略其他命令，避免在脚本执行期间接收新的脚本任务
                    }
                } else if (commandResponse.statusCode == 410) {
                    // 设备已离线，停止轮询
                    var errorResult = commandResponse.body.json();
                    log("⚠️ 脚本执行期间服务器检测到设备已离线: " + (errorResult.message || "设备已离线"));
                    log("停止命令轮询，设备需要重新连接");
                    stopPolling();
                    connected = false;

                    // 更新UI状态为断开
                    ui.run(function() {
                        try {
                            if (ui.status && typeof ui.status.setText === 'function') {
                                ui.status.setText("已断开 (设备离线)");
                                ui.status.setTextColor(colors.parseColor("#E74C3C"));
                            }
                            if (ui.connect && typeof ui.connect.setEnabled === 'function') {
                                ui.connect.setEnabled(true);
                            }
                            if (ui.disconnect && typeof ui.disconnect.setEnabled === 'function') {
                                ui.disconnect.setEnabled(false);
                            }
                        } catch (e) {
                            console.log("更新离线状态UI失败: " + e.message);
                        }
                    });

                    return;
                }

            } catch (e) {
                // 轮询错误时不输出日志，减少噪音
            }
        });
    }, 10000); // 脚本执行期间使用10秒轮询间隔，减少服务器压力但保持连接
}

// 开始服务器状态检测
function startServerStatusCheck() {
    if (serverCheckInterval) {
        clearInterval(serverCheckInterval);
    }

    isServerAlive = true;
    updateServerStatusUI("服务器状态: 正常");

    serverCheckInterval = setInterval(function() {
        if (!connected) {
            stopServerStatusCheck();
            return;
        }

        // 检测服务器是否存活
        threads.start(function() {
            try {
                var healthResponse = http.get(serverUrl + "/health", {
                    timeout: 5000
                });

                if (healthResponse && healthResponse.statusCode == 200) {
                    if (!isServerAlive) {
                        isServerAlive = true;
                        log("服务器重新连接");
                        updateServerStatusUI("服务器状态: 正常");
                    }
                } else {
                    handleServerDisconnect();
                }
            } catch (e) {
                handleServerDisconnect();
            }
        });
    }, 10000); // 每10秒检测一次
}

// 停止服务器状态检测
function stopServerStatusCheck() {
    if (serverCheckInterval) {
        clearInterval(serverCheckInterval);
        serverCheckInterval = null;
    }
    updateServerStatusUI("服务器状态: 未检测");
}

// 处理服务器断开
function handleServerDisconnect() {
    if (isServerAlive) {
        isServerAlive = false;
        log("*** 检测到服务器断开连接! ***");
        log("服务器无法访问，自动断开3002端口连接");
        updateServerStatusUI("服务器状态: 断开");

        toast("3002端口服务器已断开，仅断开群控连接");

        // 只断开3002端口的连接，不退出脚本（保持9317端口VSCode调试连接）
        setTimeout(function() {
            disconnectFromServer();
            log("=== 3002端口连接已断开，脚本继续运行 ===");
            log("注意: VSCode调试连接(9317端口)不受影响");
            toast("群控连接已断开，VSCode调试连接保持");
        }, 1000);
    }
}

// 更新服务器状态UI
function updateServerStatusUI(statusText) {
    ui.run(function() {
        try {
            if (ui.serverStatus && typeof ui.serverStatus.setText === 'function') {
                ui.serverStatus.setText(statusText);
                if (statusText.includes("正常")) {
                    ui.serverStatus.setTextColor(colors.parseColor("#27AE60"));
                } else if (statusText.includes("断开")) {
                    ui.serverStatus.setTextColor(colors.parseColor("#E74C3C"));
                } else {
                    ui.serverStatus.setTextColor(colors.parseColor("#7F8C8D"));
                }
            }
        } catch (e) {
            console.log("更新服务器状态UI失败: " + e.message);
        }
    });
}

function handlePCDisconnectCommand() {
    log("=== 处理PC端断开命令 ===");
    log("PC端要求断开连接");

    // 1. 停止轮询
    stopPolling();

    // 2. 停止当前执行的脚本
    stopCurrentScript();

    // 3. 执行断开操作
    disconnectFromServer();

    // PC端断开时，不自动退出脚本，保持脚本运行状态
    setTimeout(function() {
        log("PC端断开命令处理完成");
        toast("PC端已断开连接，脚本保持运行，可手动重新连接");
        log("提示：您可以点击'连接'按钮重新连接到服务器");
    }, 1000);
}

// 处理清空私聊记录命令
function handleClearChatRecordsCommand(command) {
    log("=== 处理清空私聊记录命令 ===");
    log("设备ID: " + (command.deviceId || "未知"));

    var success = false;
    var message = "清空失败";

    try {
        // 调用闲鱼脚本中的清空函数
        // 首先检查是否存在clearChattedPosts函数
        if (typeof clearChattedPosts === 'function') {
            log("调用闲鱼脚本的clearChattedPosts函数");
            success = clearChattedPosts();
            message = success ? "文件记录清空成功" : "文件记录清空失败";
        } else {
            // 如果函数不存在，直接操作文件
            log("clearChattedPosts函数不存在，直接清空文件");

            var dataFile = "/storage/emulated/0/脚本/咸鱼/data/chatted_posts.json";

            try {
                // 确保目录存在
                var dir = "/storage/emulated/0/脚本/咸鱼/data/";
                if (!files.exists(dir)) {
                    files.ensureDir(dir);
                    log("📁 创建数据目录：" + dir);
                }

                // 清空文件内容（写入空数组）
                files.write(dataFile, JSON.stringify([], null, 2));
                log("✅ 私聊记录文件已清空: " + dataFile);
                success = true;
                message = "文件记录清空成功";

                // 如果存在全局变量，也清空它
                if (typeof chattedPosts !== 'undefined') {
                    chattedPosts = [];
                    log("✅ 全局变量chattedPosts已清空");
                }

            } catch (fileError) {
                log("❌ 清空文件失败: " + fileError.message);
                success = false;
                message = "文件清空失败: " + fileError.message;
            }
        }

        log("私聊记录清空结果: " + (success ? "成功" : "失败"));
        toast("私聊记录" + (success ? "清空成功" : "清空失败"));

    } catch (error) {
        log("❌ 处理清空命令失败: " + error.message);
        success = false;
        message = "处理失败: " + error.message;
    }

    // 发送响应给服务器
    try {
        threads.start(function() {
            try {
                var responseData = {
                    type: 'clearChatRecordsResponse',
                    deviceId: command.deviceId,
                    success: success,
                    message: message,
                    timestamp: Date.now()
                };

                // 通过HTTP发送响应（因为我们使用的是轮询而不是WebSocket）
                var response = http.postJson(serverUrl + "/api/device/clear-chat-records-response", responseData, {
                    headers: {
                        "Authorization": "Bearer " + token,
                        "Content-Type": "application/json"
                    },
                    timeout: 5000
                });

                if (response && response.statusCode === 200) {
                    log("✅ 清空结果响应发送成功");
                } else {
                    log("❌ 清空结果响应发送失败: " + (response ? response.statusCode : "无响应"));
                }

            } catch (e) {
                log("❌ 发送清空结果响应失败: " + e.message);
            }
        });
    } catch (e) {
        log("❌ 启动响应发送线程失败: " + e.message);
    }

    log("清空私聊记录命令处理完成");
}

// 处理视频传输命令
function handleVideoTransferCommand(command) {
    log("=== 处理视频传输命令 ===");
    log("任务ID: " + (command.taskId || "未知"));
    log("视频数量: " + (command.videos ? command.videos.length : 0));

    if (!command.videos || command.videos.length === 0) {
        log("❌ 没有要传输的视频");
        toast("没有要传输的视频");
        return;
    }

    toast("开始传输 " + command.videos.length + " 个视频文件");

    // 在后台线程中处理视频传输
    threads.start(function() {
        try {
            log("开始后台视频传输任务");

            // 创建下载目录
            var downloadDir = "/sdcard/AutoJS_Videos/";
            var albumDir = "/sdcard/DCIM/Camera/";

            if (!files.exists(downloadDir)) {
                files.createWithDirs(downloadDir);
                log("创建下载目录: " + downloadDir);
            }

            if (!files.exists(albumDir)) {
                files.createWithDirs(albumDir);
                log("创建相册目录: " + albumDir);
            }

            var successCount = 0;
            var failedCount = 0;
            var results = [];

            // 逐个下载视频
            for (var i = 0; i < command.videos.length; i++) {
                var video = command.videos[i];
                log("处理第 " + (i + 1) + " 个视频: " + video.original_name);

                try {
                    var downloadUrl = "http://" + command.serverHost + "/api/xiaohongshu/download-video/" + video.id;
                    var tempFilePath = downloadDir + video.original_name;
                    var albumFilePath = albumDir + "AutoJS_" + video.original_name;

                    log("下载URL: " + downloadUrl);
                    log("相册路径: " + albumFilePath);

                    // 检查文件是否已存在
                    if (files.exists(albumFilePath)) {
                        log("文件已存在，跳过下载: " + albumFilePath);
                        successCount++;
                        results.push({
                            videoName: video.original_name,
                            success: true,
                            message: "文件已存在"
                        });
                        continue;
                    }

                    // 发送下载请求
                    var response = http.get(downloadUrl, {
                        headers: {
                            "Authorization": "Bearer " + (command.authToken || "test-token")
                        }
                    });

                    if (response && response.statusCode === 200) {
                        // 先保存到临时目录
                        files.writeBytes(tempFilePath, response.body.bytes());
                        log("文件保存到临时目录成功");

                        // 复制到相册目录
                        if (files.copy(tempFilePath, albumFilePath)) {
                            log("文件复制到相册成功: " + albumFilePath);

                            // 删除临时文件
                            files.remove(tempFilePath);

                            // 通知媒体扫描器
                            try {
                                var intent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
                                intent.setData(android.net.Uri.fromFile(new java.io.File(albumFilePath)));
                                context.sendBroadcast(intent);
                                log("已通知系统更新相册");
                            } catch (e) {
                                log("通知系统更新相册失败: " + e.message);
                            }

                            successCount++;
                            results.push({
                                videoName: video.original_name,
                                success: true,
                                message: "下载成功"
                            });

                        } else {
                            log("复制到相册失败");
                            failedCount++;
                            results.push({
                                videoName: video.original_name,
                                success: false,
                                message: "复制到相册失败"
                            });
                        }
                    } else {
                        throw new Error("下载失败，状态码: " + (response ? response.statusCode : "无响应"));
                    }

                } catch (error) {
                    log("下载视频失败: " + error.message);
                    failedCount++;
                    results.push({
                        videoName: video.original_name,
                        success: false,
                        message: error.message
                    });
                }

                // 等待一下再下载下一个
                sleep(1000);
            }

            // 显示传输结果
            var resultMessage = "视频传输完成！成功: " + successCount + ", 失败: " + failedCount;
            log(resultMessage);
            toast(resultMessage);

            // 列出传输结果
            for (var j = 0; j < results.length; j++) {
                var result = results[j];
                var status = result.success ? "✅" : "❌";
                log(status + " " + result.videoName + " - " + result.message);
            }

        } catch (error) {
            log("❌ 视频传输任务失败: " + error.message);
            toast("视频传输失败: " + error.message);
        }
    });

    log("视频传输命令处理完成，后台任务已启动");
}

// 闲鱼脚本安全执行函数
function executeXianyuScriptSafely(scriptCode) {
    try {
        log("=== 开始安全执行闲鱼脚本 ===");

        // 设置全局变量供闲鱼脚本使用
        global.globalServerUrl = serverUrl;
        global.globalDeviceId = deviceId;
        global.globalDeviceName = "闲鱼设备"; // 可以从设备信息中获取
        global.globalToken = token;

        log("设置闲鱼脚本全局变量:");
        log("- 服务器地址: " + serverUrl);
        log("- 设备ID: " + deviceId);
        log("- Token: " + (token ? "已设置" : "未设置"));

        // 直接在当前线程中执行，但使用更强的异常处理
        try {
            log("开始执行闲鱼脚本代码");
            eval(scriptCode);
            log("✅ 闲鱼脚本执行完成");

            // 检查闲鱼脚本的实际执行结果
            var actualSuccess = true;
            var resultMessage = "闲鱼脚本执行成功";

            // 检查是否有全局变量表示执行失败
            if (typeof global !== 'undefined') {
                if (global.xianyuExecutionFailed === true) {
                    actualSuccess = false;
                    resultMessage = global.xianyuFailureReason || "闲鱼脚本执行失败";
                    log("❌ 检测到闲鱼脚本执行失败: " + resultMessage);
                } else if (global.xianyuExecutionSuccess === false) {
                    actualSuccess = false;
                    resultMessage = "闲鱼脚本未成功完成任务";
                    log("❌ 检测到闲鱼脚本未成功完成: " + resultMessage);
                }
            }

            // 检查控制台日志中的失败标识
            if (actualSuccess) {
                // 这里可以添加更多的检查逻辑
                // 比如检查特定的全局变量或状态
                log("✅ 闲鱼脚本执行成功，未检测到失败标识");
            }

            return { success: actualSuccess, message: resultMessage };

        } catch (evalError) {
            log("❌ 闲鱼脚本执行错误: " + evalError.message);
            log("错误堆栈: " + (evalError.stack || "无堆栈信息"));

            // 根据错误类型判断是否为严重错误
            var errorMsg = evalError.message || evalError.toString();
            if (errorMsg.includes("ScriptInterruptedException") ||
                errorMsg.includes("脚本被中断") ||
                errorMsg.includes("脚本被终止")) {
                log("检测到脚本中断，这是正常的停止操作");
                return { success: false, message: "脚本被用户停止" };
            }

            return { success: false, message: "闲鱼脚本执行失败: " + errorMsg };
        }

    } catch (error) {
        log("❌ 闲鱼脚本安全执行失败: " + error.message);
        log("错误详情: " + (error.stack || "无堆栈信息"));

        // 确保不会因为闲鱼脚本错误导致双向.js退出
        return { success: false, message: "闲鱼脚本执行失败: " + error.message };
    }
}

function executeScript(scriptCode, logId) {
    if (!scriptCode) {
        log("执行脚本失败: 脚本代码为空");
        sendStatusUpdate('error', '脚本出错', 0, '脚本代码为空');
        return;
    }

    // 保存当前脚本内容，用于任务类型检测
    currentScriptContent = scriptCode;

    log("执行脚本: " + (scriptCode.length > 30 ? scriptCode.substring(0, 30) + "..." : scriptCode));

    try {
        // 如果有正在执行的脚本，先停止它
        if (currentScriptThread) {
            log("停止之前的脚本线程");
            try {
                currentScriptThread.interrupt();
                currentScriptThread = null; // 立即清空引用
                log("之前的脚本线程已停止");
            } catch (e) {
                log("停止之前脚本线程失败: " + e.message);
            }

            // 等待100毫秒，确保中断状态完全清理
            sleep(100);
            log("脚本中断状态已清理，准备启动新脚本");
        }

        // 发送开始状态
        sendStatusUpdate('starting', '等待开始', 0, '等待开始执行');

        // 启动新的脚本线程
        currentScriptThread = threads.start(function() {
            var result = "";
            var status = "success";

            try {
                log("开始执行下发脚本，脚本长度: " + scriptCode.length);

                // 步骤1: 发送脚本执行中状态
                sendStatusUpdate('app_opening', '脚本执行中', 50, '正在打开小红书应用');
                log("步骤1: 正在打开小红书应用");

                // 模拟打开应用的延迟和检查
                sleep(1000);
                log("检查小红书应用是否已安装");

                // 模拟应用启动过程
                log("启动小红书应用");
                sleep(1000);
                log("等待应用加载完成");

                // 创建隔离的执行环境，防止脚本错误影响控制器
                var isolatedExecution = function() {
                    try {
                        // 检查线程中断状态，如果被中断则跳过执行
                        if (threads.currentThread().isInterrupted()) {
                            log("检测到线程中断状态，跳过脚本执行");
                            return { success: false, message: "脚本启动时检测到中断状态" };
                        }

                        // 步骤2: 发送执行中状态
                        sendStatusUpdate('executing', '脚本执行中', 75, '脚本正在执行中');
                        log("步骤2: 开始执行脚本逻辑");

                        // 检查是否是轻量级搜索加群脚本
                        if (scriptCode.includes('轻量级搜索加群脚本')) {
                            log("检测到轻量级搜索加群脚本，在双向.js环境中安全执行");
                        }

                        // 检查是否是闲鱼脚本，需要特殊处理
                        if (scriptCode.includes('闲鱼关键词私信') || scriptCode.includes('startAutoChat')) {
                            log("检测到闲鱼自动化脚本，启用增强错误处理");
                            return executeXianyuScriptSafely(scriptCode);
                        }

                        // 在隔离环境中执行脚本
                        log("执行脚本代码...");

                        // 检查脚本是否包含可能导致退出的危险命令
                        var dangerousCommands = ['exit()', 'engines.stopAll()', 'engines.stopAllAndToast()'];
                        var hasDangerousCommand = false;
                        for (var i = 0; i < dangerousCommands.length; i++) {
                            if (scriptCode.includes(dangerousCommands[i])) {
                                log("警告: 检测到可能导致脚本退出的命令: " + dangerousCommands[i]);
                                hasDangerousCommand = true;
                            }
                        }

                        if (hasDangerousCommand) {
                            log("脚本包含危险命令，使用安全模式执行");
                            // 移除或替换危险命令
                            var safeScriptCode = scriptCode
                                .replace(/exit\(\)/g, 'log("exit()命令已被安全模式拦截")')
                                .replace(/engines\.stopAll\(\)/g, 'log("engines.stopAll()命令已被安全模式拦截")')
                                .replace(/engines\.stopAllAndToast\([^)]*\)/g, 'log("engines.stopAllAndToast()命令已被安全模式拦截")');

                            eval(safeScriptCode);
                        } else {
                            eval(scriptCode);
                        }

                        log("脚本代码执行完成");
                        return { success: true, message: "脚本执行成功" };
                    } catch (innerError) {
                        // 检查是否是中断异常
                        if (innerError.message && innerError.message.includes("ScriptInterruptedException")) {
                            log("检测到脚本中断异常，这通常是正常的脚本切换过程");
                            return { success: false, message: "脚本被中断（正常切换）" };
                        }

                        var errorMsg = "脚本内部执行错误: " + innerError.message;
                        log(errorMsg);
                        log("错误堆栈: " + (innerError.stack || "无堆栈信息"));
                        return { success: false, message: errorMsg };
                    }
                };

                var executionResult = isolatedExecution();

                if (executionResult.success) {
                    result = executionResult.message;
                    status = "success";  // 显式设置成功状态
                    log("步骤3: 脚本执行成功");
                    // 发送完成状态
                    sendStatusUpdate('completed', '完成', 100, '脚本执行完成');
                    log("所有步骤执行完成");

                    // 脚本执行完成后关闭小红书应用（在后台线程中执行）
                    log("脚本执行完成，5秒后关闭小红书应用");
                    threads.start(function() {
                        sleep(5000);
                        closeXiaohongshuApp();
                    });
                } else {
                    result = executionResult.message;
                    status = "error";
                    log("脚本执行失败: " + executionResult.message);
                    // 发送错误状态
                    sendStatusUpdate('error', '脚本出错', 0, executionResult.message);

                    // 脚本执行失败后关闭小红书应用（在后台线程中执行）
                    log("脚本执行失败，5秒后关闭小红书应用");
                    threads.start(function() {
                        sleep(5000);
                        closeXiaohongshuApp();
                    });
                }

            } catch (e) {
                result = "脚本执行失败: " + e.message;
                status = "error";
                var errorMsg = "脚本执行失败: " + e.message;
                log(errorMsg);
                log("错误详情: " + (e.stack || "无堆栈信息"));
                // 发送错误状态
                sendStatusUpdate('error', '脚本出错', 0, errorMsg);

                // 脚本执行出错后关闭小红书应用（在后台线程中执行）
                log("脚本执行出错，5秒后关闭小红书应用");
                threads.start(function() {
                    sleep(5000);
                    closeXiaohongshuApp();
                });
            } finally {
                // 脚本执行完成，清空当前线程引用
                currentScriptThread = null;
                currentTaskId = null;
                log("脚本执行完成，清理线程引用");

                // 脚本执行完成后，恢复正常轮询
                if (connected) {
                    log("脚本执行完成，恢复正常轮询");
                    isScriptExecutionPolling = false;
                    startPolling();
                } else {
                    log("设备已断开连接，不恢复轮询");
                }
            }

            if (logId && token) {
                threads.start(function() {
                    try {
                        var resultResponse = http.postJson(serverUrl + "/api/device/" + deviceId + "/result", {
                            logId: logId,
                            result: result,
                            status: status
                        }, {
                            headers: {
                                "Authorization": "Bearer " + token
                            },
                            timeout: 5000
                        });

                        if (resultResponse.statusCode == 200) {
                            log("执行结果已上报");
                        }
                    } catch (e) {
                        log("上报结果错误: " + e.message);
                    }
                });
            }
        });
    } catch (e) {
        log("脚本启动失败: " + e.message);
        currentScriptThread = null;
    }
}



// 处理停止脚本命令
function handleStopScriptCommand(command) {
    log("=== 处理PC端停止脚本命令 ===");
    log("任务ID: " + (command.taskId || "未知"));

    if (currentScriptThread) {
        log("正在停止当前执行的脚本线程...");
        try {
            // 发送停止状态
            sendStatusUpdate('stopped', '已停止', 0, '脚本已被PC端停止');

            // 如果是视频发布脚本，设置停止标志
            if (currentScriptContent && currentScriptContent.includes("setStopFlag")) {
                log("检测到视频发布脚本，设置停止标志");
                try {
                    // 尝试调用全局停止函数
                    if (typeof global !== 'undefined' && global.setStopFlag) {
                        global.setStopFlag();
                        log("全局停止标志已设置");
                    } else {
                        // 备用方法：直接设置全局变量
                        if (typeof global !== 'undefined') {
                            global.shouldStop = true;
                            log("全局停止变量已设置");
                        }
                    }
                } catch (e) {
                    log("设置停止标志失败: " + e.message);
                }
            }

            currentScriptThread.interrupt();
            currentScriptThread = null;
            currentTaskId = null;
            log("脚本线程已成功停止");
            toast("当前脚本已停止");

            // 通知服务器脚本已停止
            if (command.logId && token) {
                threads.start(function() {
                    try {
                        var stopResponse = http.postJson(serverUrl + "/api/device/" + deviceId + "/result", {
                            logId: command.logId,
                            result: "脚本已被PC端停止",
                            status: "stopped"
                        }, {
                            headers: {
                                "Authorization": "Bearer " + token
                            },
                            timeout: 5000
                        });

                        if (stopResponse.statusCode == 200) {
                            log("脚本停止状态已上报");
                        }
                    } catch (e) {
                        log("上报停止状态错误: " + e.message);
                    }
                });
            }

            // 上报脚本停止状态
            log("上报脚本停止状态到服务器");
            reportScriptStopStatus(command.reason || "PC端停止");

            // 脚本停止后关闭小红书应用（在后台线程中执行）
            log("脚本被停止，5秒后关闭小红书应用");
            threads.start(function() {
                sleep(5000);
                closeXiaohongshuApp();
            });

        } catch (e) {
            log("停止脚本线程失败: " + e.message);
            toast("停止脚本失败: " + e.message);
        }
    } else {
        log("当前没有正在执行的脚本");
        toast("当前没有正在执行的脚本");
    }

    log("脚本停止处理完成，继续轮询等待新命令");

    // 恢复轮询
    if (connected) {
        startPolling();
    }
}

function disconnectFromServer() {
    log("=== 手机端主动断开 ===");

    // 保存当前的连接信息，避免在重置过程中被清空
    var currentDeviceId = deviceId;
    var currentConnected = connected;

    // 1. 首先停止轮询，防止内存泄漏
    stopPolling();

    // 2. 停止服务器状态检测
    stopServerStatusCheck();

    // 3. 停止当前脚本线程
    stopCurrentScript();

    log("断开前检查:");
    log("connected: " + currentConnected);
    log("deviceId: " + (currentDeviceId ? currentDeviceId : "不存在"));

    if (currentConnected && currentDeviceId) {
        threads.start(function() {
            try {
                log("通知服务器手机端断开...");
                log("URL: " + serverUrl + "/api/device/" + currentDeviceId + "/disconnect");

                // 添加调试信息
                log("发送断开请求前的详细信息:");
                log("- 服务器URL: " + serverUrl);
                log("- 设备ID: " + currentDeviceId);
                log("- 请求方法: POST");
                log("- 请求头: Content-Type");

                var response = http.post(serverUrl + "/api/device/" + currentDeviceId + "/disconnect", {}, {
                    headers: {
                        "Content-Type": "application/json"
                    },
                    timeout: 10000
                });

                log("断开响应状态码: " + (response ? response.statusCode : "null"));

                if (response) {
                    log("响应头信息: " + JSON.stringify(response.headers));
                    if (response.statusCode == 200) {
                        try {
                            if (response.body) {
                                var responseText = response.body.string();
                                log("原始响应内容: " + responseText);

                                try {
                                    var result = JSON.parse(responseText);
                                    log("服务器确认断开成功: " + result.message);
                                    log("*** PC端会实时更新设备状态 ***");
                                    toast("断开成功，PC端会实时更新");

                                    // 断开成功后，执行清理操作
                                    setTimeout(function() {
                                        resetAllStates();
                                        closeCurrentApps();
                                    }, 100);
                                } catch (jsonError) {
                                    log("JSON解析错误: " + jsonError.message);
                                    log("非JSON响应内容: " + responseText);
                                    toast("断开成功（响应格式异常）");
                                }
                            } else {
                                log("服务器确认断开成功（无响应体）");
                                toast("断开成功");

                                // 断开成功后，执行清理操作
                                setTimeout(function() {
                                    resetAllStates();
                                    closeCurrentApps();
                                }, 100);
                            }
                        } catch (parseError) {
                            log("响应解析错误: " + parseError.message);
                            log("原始响应: " + (response.body ? response.body.string() : "无响应体"));
                            log("但状态码200表示断开成功");
                            toast("断开成功");

                            // 断开成功后，执行清理操作
                            setTimeout(function() {
                                resetAllStates();
                                closeCurrentApps();
                            }, 100);
                        }
                    } else {
                        log("服务器断开失败，状态码: " + response.statusCode);
                        try {
                            if (response.body) {
                                var responseText = response.body.string();
                                log("错误响应内容: " + responseText);

                                try {
                                    var errorResult = JSON.parse(responseText);
                                    log("错误信息: " + errorResult.message);
                                } catch (jsonError) {
                                    log("错误响应非JSON格式: " + responseText);
                                }
                            }
                        } catch (parseError) {
                            log("错误响应解析失败: " + parseError.message);
                        }
                    }
                } else {
                    log("断开响应为空");
                }

            } catch (e) {
                log("断开请求异常: " + e.message);
                log("异常详情: " + e.stack);

                // 即使断开请求失败，也要执行本地清理操作
                setTimeout(function() {
                    resetAllStates();
                    closeCurrentApps();
                }, 100);
            }
        });
    } else {
        log("无法断开: 设备未连接或缺少必要信息");
        log("connected: " + currentConnected);
        log("deviceId: " + (currentDeviceId ? currentDeviceId : "不存在"));

        // 即使无法发送断开请求，也要执行本地清理操作
        setTimeout(function() {
            resetAllStates();
            closeCurrentApps();
        }, 100);
    }

    // 注意：状态重置现在在resetAllStates()中处理，避免重复操作

    // 强制垃圾回收，释放内存
    try {
        java.lang.System.gc();
        log("已执行垃圾回收");
    } catch (e) {
        log("垃圾回收失败: " + e.message);
    }

    log("本地状态已更新为断开");
    log("=== 双向断开测试完成 ===");
    log("注意: 如果需要验证断开效果，请等待5秒后再重新连接");

    // 添加确认对话框，防止用户立即重连
    ui.run(function() {
        setTimeout(function() {
            dialogs.build({
                title: "断开成功",
                content: "设备已成功断开连接。\n\n请不要立即点击连接按钮，否则设备会立即重连，导致web页面看不到设备离线状态。\n\n建议等待至少5秒后再重新连接。",
                positive: "我知道了",
                negative: "退出脚本",
                cancelable: false
            }).on("negative", () => {
                // 用户选择退出脚本
                log("用户选择退出脚本");

                // 停止脚本保护
                if (typeof scriptProtection !== 'undefined') {
                    scriptProtection.stopProtection();
                }

                exit();
            }).show();
        }, 500);
    });
}

function clear() {
    try {
        if (ui.log && typeof ui.log.setText === 'function') {
            ui.log.setText("日志已清空");
        }
    } catch (e) {
        console.log("清空日志UI失败: " + e.message);
    }
}

// 初始化时获取基本设备信息
getDeviceInfo();

ui.connect.click(connectToServer);
ui.disconnect.click(disconnectFromServer);
ui.clear.click(clear);

// // 监听脚本退出事件
// events.on("exit", function() {
//     log("=== 脚本即将退出 ===");
//     if (connected) {
//         log("脚本退出时自动断开连接");
//         // 通知服务器设备主动断开
//         notifyServerOnExit();
//     }
//     // 清理资源
//     stopPolling();
//     stopServerStatusCheck();
// });

// 关闭当前正在运行的应用
function closeCurrentApps() {
    log("=== 关闭当前正在运行的应用 ===");

    // 使用线程执行，避免UI线程阻塞
    threads.start(function() {
        try {
            // 检查当前运行的应用包名
            var currentPkg = currentPackage();
            log("当前应用包名: " + (currentPkg || "桌面"));

            // 如果是小红书应用，使用专门的关闭方法
            if (currentPkg && currentPkg.includes("xhs")) {
                log("检测到小红书正在运行，开始关闭");
                closeXiaohongshuApp();
            }
            // 如果是闲鱼应用
            else if (currentPkg && (currentPkg.includes("taobao") || currentPkg.includes("xianyu"))) {
                log("检测到闲鱼正在运行，开始关闭");
                closeXianyuApp();
            }
            // 其他应用使用通用关闭方法
            else if (currentPkg && !currentPkg.includes("launcher") && !currentPkg.includes("home") && !currentPkg.includes("desktop")) {
                log("检测到其他应用正在运行，使用通用关闭方法");
                closeGenericApp();
            }

            // 最后确保回到桌面
            log("确保回到桌面");
            home();
            sleep(1000);

            log("应用关闭处理完成");
        } catch (e) {
            log("关闭应用时出错: " + e.message);
            // 备用方法：直接按Home键
            log("使用备用方法：按Home键");
            home();
            sleep(1000);
        }
    });
}

// 关闭闲鱼应用
function closeXianyuApp() {
    try {
        log("开始关闭闲鱼应用");

        // 使用返回键多次退出
        for (let i = 0; i < 10; i++) {
            back();
            sleep(300);

            // 检查是否已经退出到桌面
            let pkg = currentPackage();
            if (!pkg || pkg.includes("launcher") || pkg.includes("home") || pkg.includes("desktop")) {
                log("✅ 闲鱼已退出到桌面");
                return true;
            }
        }

        // 如果还没退出，按Home键
        home();
        sleep(1000);
        log("✅ 闲鱼应用关闭完成");
        return true;
    } catch (e) {
        log("❌ 关闭闲鱼应用时出错: " + e.message);
        home();
        sleep(1000);
        return true;
    }
}

// 关闭通用应用
function closeGenericApp() {
    try {
        log("使用通用方法关闭应用");

        // 使用返回键退出
        for (let i = 0; i < 5; i++) {
            back();
            sleep(300);

            // 检查是否已经退出到桌面
            let pkg = currentPackage();
            if (!pkg || pkg.includes("launcher") || pkg.includes("home") || pkg.includes("desktop")) {
                log("✅ 应用已退出到桌面");
                return true;
            }
        }

        // 如果还没退出，按Home键
        home();
        sleep(1000);
        log("✅ 通用应用关闭完成");
        return true;
    } catch (e) {
        log("❌ 关闭通用应用时出错: " + e.message);
        home();
        sleep(1000);
        return true;
    }
}

// 停止当前执行的脚本
function stopCurrentScript() {
    log("=== 停止当前执行的脚本 ===");

    try {
        // 停止当前脚本线程
        if (currentScriptThread) {
            log("停止当前脚本线程");

            // 安全地中断脚本线程
            try {
                // 检查线程是否还活着
                if (currentScriptThread.isAlive()) {
                    log("脚本线程正在运行，发送中断信号");
                    currentScriptThread.interrupt();

                    // 等待一小段时间让线程自然结束
                    setTimeout(function() {
                        if (currentScriptThread && currentScriptThread.isAlive()) {
                            log("⚠️ 脚本线程仍在运行，强制停止");
                            try {
                                currentScriptThread.interrupt();
                            } catch (forceStopError) {
                                log("强制停止脚本线程失败: " + forceStopError.message);
                            }
                        }
                        currentScriptThread = null;
                    }, 1000);
                } else {
                    log("脚本线程已经停止");
                    currentScriptThread = null;
                }
            } catch (interruptError) {
                log("中断脚本线程时出错: " + interruptError.message);
                currentScriptThread = null;
            }

            log("✅ 脚本线程停止处理完成");
        } else {
            log("当前没有正在执行的脚本线程");
        }

        // 设置停止标志
        if (typeof shouldStopScript !== 'undefined') {
            shouldStopScript = true;
            log("✅ 脚本停止标志已设置");
        }

    } catch (e) {
        log("❌ 停止脚本时出错: " + e.message);
        // 确保即使出错也清理线程引用
        currentScriptThread = null;
    }
}

// 重置所有状态变量
function resetAllStates() {
    log("=== 重置所有状态变量 ===");

    try {
        // 重置连接状态
        connected = false;
        isServerAlive = false;

        // 重置脚本执行状态
        currentScriptThread = null;
        if (typeof shouldStopScript !== 'undefined') {
            shouldStopScript = false;
        }

        // 重置UI状态（确保在UI线程中执行）
        ui.run(function() {
            try {
                if (ui.status && typeof ui.status.setText === 'function') {
                    ui.status.setText("已断开");
                    ui.status.setTextColor(colors.parseColor("#E74C3C"));
                }
                if (ui.deviceInfo && typeof ui.deviceInfo.setText === 'function') {
                    ui.deviceInfo.setText("设备ID: 未生成");
                }
                if (ui.serverStatus && typeof ui.serverStatus.setText === 'function') {
                    ui.serverStatus.setText("服务器状态: 未检测");
                    ui.serverStatus.setTextColor(colors.parseColor("#7F8C8D"));
                }
                if (ui.connect && typeof ui.connect.setEnabled === 'function') {
                    ui.connect.setEnabled(true);
                }
                if (ui.disconnect && typeof ui.disconnect.setEnabled === 'function') {
                    ui.disconnect.setEnabled(false);
                }
            } catch (e) {
                log("重置UI状态时出错: " + e.message);
            }
        });

        log("✅ 所有状态变量重置完成");
    } catch (e) {
        log("❌ 重置状态变量时出错: " + e.message);
    }
}

// 未使用的函数已移除

// ===== 设备应用检测功能 =====

/**
 * 检测设备上的小红书和闲鱼应用
 */
function detectDeviceApps() {
    if (!connected) {
        log("设备未连接，跳过应用检测");
        return;
    }

    log("=== 开始检测设备应用 ===");

    threads.start(function() {
        try {
            // 先回到桌面
            home();
            sleep(2000);

            var appResults = searchDeviceApps();

            if (appResults) {
                log("应用检测完成，准备上报服务器");
                reportAppsToServer(appResults);
            } else {
                log("应用检测失败");
            }

        } catch (e) {
            log("应用检测过程出错: " + e.message);
        }
    });
}

/**
 * 搜索设备上的小红书和闲鱼应用
 */
function searchDeviceApps() {
    var allResults = {
        xiaohongshu: [],
        xianyu: []
    };

    log("开始全面搜索小红书和闲鱼应用...");

    try {
        // 1. 查找包含"小红书"的应用
        log("=== 搜索小红书相关应用 ===");
        var xiaohongshuByKeyword = findAppsByKeyword("小红书");
        allResults.xiaohongshu = allResults.xiaohongshu.concat(xiaohongshuByKeyword);

        // 2. 使用正则表达式查找小红书
        var xiaohongshuByRegex = findAppsByRegex(".*小红书.*", "小红书相关应用");
        allResults.xiaohongshu = allResults.xiaohongshu.concat(xiaohongshuByRegex);

        // 3. 查找包含"闲鱼"的应用
        log("=== 搜索闲鱼相关应用 ===");
        var xianyuByKeyword = findAppsByKeyword("闲鱼");
        allResults.xianyu = allResults.xianyu.concat(xianyuByKeyword);

        // 4. 使用正则表达式查找闲鱼
        var xianyuByRegex = findAppsByRegex(".*闲鱼.*", "闲鱼相关应用");
        allResults.xianyu = allResults.xianyu.concat(xianyuByRegex);

        // 去重
        allResults.xiaohongshu = removeDuplicateApps(allResults.xiaohongshu);
        allResults.xianyu = removeDuplicateApps(allResults.xianyu);

        log("小红书应用数量: " + allResults.xiaohongshu.length);
        log("闲鱼应用数量: " + allResults.xianyu.length);

        return allResults;

    } catch (e) {
        log("搜索应用过程出错: " + e.message);
        return null;
    }
}

/**
 * 查找包含指定关键词的TextView元素
 */
function findAppsByKeyword(keyword) {
    log("🔍 查找包含 \"" + keyword + "\" 的应用...");

    var foundElements = [];

    try {
        // 获取所有TextView元素
        var allTextViews = className("android.widget.TextView").find();
        log("扫描 " + allTextViews.length + " 个TextView元素");

        for (var i = 0; i < allTextViews.length; i++) {
            var textView = allTextViews[i];
            var text = textView.text();

            if (text && text.indexOf(keyword) !== -1) {
                // 过滤掉日志文本和其他无关文本
                if (isValidAppText(text)) {
                    var appInfo = {
                        text: text,
                        bounds: textView.bounds().toString(),
                        clickable: textView.clickable(),
                        method: "keyword"
                    };
                    foundElements.push(appInfo);

                    log("✅ 找到匹配项：\"" + text + "\"");
                } else {
                    log("⚠️ 跳过无效文本：\"" + text.substring(0, 50) + (text.length > 50 ? "..." : "") + "\"");
                }
            }
        }

        if (foundElements.length === 0) {
            log("❌ 未找到包含 \"" + keyword + "\" 的应用");
        }

        return foundElements;

    } catch (error) {
        log("查找过程出错：" + error.message);
        return foundElements;
    }
}

/**
 * 验证文本是否为有效的应用文本
 */
function isValidAppText(text) {
    // 过滤条件
    var filters = [
        // 1. 过滤日志文本（包含时间戳）
        /\[\d{2}:\d{2}:\d{2}\]/,
        // 2. 过滤包含换行符的长文本
        /\n/,
        // 3. 过滤过长的文本（超过50个字符）
        function(text) { return text.length > 50; },
        // 4. 过滤包含"步骤"、"开始"、"完成"等日志关键词的文本
        /步骤|开始|完成|检测|搜索|查找|轮询|监控|注册|连接|断开/,
        // 5. 过滤包含特殊符号的文本
        /[=]{3,}|[🔍📱💾📢🛑]/
    ];

    for (var i = 0; i < filters.length; i++) {
        var filter = filters[i];
        if (typeof filter === 'function') {
            if (filter(text)) {
                return false;
            }
        } else if (filter.test && filter.test(text)) {
            return false;
        }
    }

    // 只保留简短的、可能是应用名称的文本
    return text.length <= 20 && text.trim() === text;
}

/**
 * 使用正则表达式查找应用
 */
function findAppsByRegex(pattern, description) {
    log("🔍 使用正则表达式查找：" + description);

    var foundElements = [];

    try {
        var elements = className("android.widget.TextView").textMatches(pattern).find();

        for (var i = 0; i < elements.length; i++) {
            var element = elements[i];
            var text = element.text();

            // 使用相同的过滤逻辑
            if (isValidAppText(text)) {
                var appInfo = {
                    text: text,
                    bounds: element.bounds().toString(),
                    clickable: element.clickable(),
                    method: "regex"
                };
                foundElements.push(appInfo);

                log("✅ 正则匹配找到：\"" + text + "\"");
            } else {
                log("⚠️ 正则匹配跳过无效文本：\"" + text.substring(0, 50) + (text.length > 50 ? "..." : "") + "\"");
            }
        }

        if (foundElements.length === 0) {
            log("❌ 正则表达式未找到匹配项");
        }

        return foundElements;

    } catch (error) {
        log("正则查找出错：" + error.message);
        return foundElements;
    }
}

/**
 * 去除重复项
 */
function removeDuplicateApps(apps) {
    var unique = [];
    var seenTexts = [];

    for (var i = 0; i < apps.length; i++) {
        var app = apps[i];
        var found = false;
        for (var j = 0; j < seenTexts.length; j++) {
            if (seenTexts[j] === app.text) {
                found = true;
                break;
            }
        }
        if (!found) {
            seenTexts.push(app.text);
            unique.push(app);
        }
    }

    return unique;
}

/**
 * 将检测到的应用信息上报给服务器
 */
function reportAppsToServer(appResults) {
    if (!connected || !deviceId) {
        log("设备未连接或缺少设备ID，无法上报应用信息");
        return;
    }

    log("准备上报应用信息到服务器...");

    threads.start(function() {
        try {
            var reportData = {
                apps: {
                    xiaohongshu: appResults.xiaohongshu,
                    xianyu: appResults.xianyu
                },
                detectedAt: new Date().toISOString()
            };

            log("上报数据: " + JSON.stringify(reportData, null, 2));

            var response = http.postJson(serverUrl + "/api/device/" + deviceId + "/apps", reportData, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 10000
            });

            if (response && response.statusCode == 200) {
                var result = response.body.json();
                if (result.success) {
                    log("✅ 应用信息上报成功");
                    log("服务器响应: " + JSON.stringify(result, null, 2));
                } else {
                    log("❌ 应用信息上报失败: " + result.message);
                }
            } else {
                log("❌ 应用信息上报请求失败，状态码: " + (response ? response.statusCode : "null"));
                if (response && response.body) {
                    try {
                        var errorResult = response.body.json();
                        log("错误详情: " + JSON.stringify(errorResult, null, 2));
                    } catch (e) {
                        log("错误响应体: " + response.body.string());
                    }
                }
            }

        } catch (e) {
            log("上报应用信息出错: " + e.message);
        }
    });
}

// 处理脚本执行完成通知
function handleScriptCompletedNotification(command) {
    log("开始处理脚本执行完成通知");
    log("通知详情: " + JSON.stringify(command, null, 2));

    try {
        // 确保当前没有正在执行的脚本
        if (currentScriptThread && !currentScriptThread.isAlive()) {
            log("当前脚本线程已结束，清理线程引用");
            currentScriptThread = null;
            currentTaskId = null;
        }

        // 如果还有脚本在执行，记录警告但不强制停止
        if (currentScriptThread && currentScriptThread.isAlive()) {
            log("⚠️ 警告: 收到脚本完成通知，但检测到脚本仍在执行");
            log("脚本线程状态: " + (currentScriptThread.isAlive() ? "运行中" : "已结束"));
            log("当前任务ID: " + currentTaskId);
        } else {
            log("✅ 确认当前没有脚本在执行，状态正常");
        }

        // 发送确认响应给服务器
        threads.start(function() {
            try {
                var responseData = {
                    type: 'script_completion_acknowledged',
                    deviceId: deviceId,
                    taskId: command.taskId || currentTaskId,
                    message: '设备端已确认脚本执行完成',
                    hasRunningScript: currentScriptThread && currentScriptThread.isAlive(),
                    timestamp: Date.now()
                };

                var response = http.postJson(serverUrl + "/api/device/script-completion-ack", responseData, {
                    headers: {
                        "Authorization": "Bearer " + token,
                        "Content-Type": "application/json"
                    },
                    timeout: 3000
                });

                if (response && response.statusCode == 200) {
                    log("✅ 脚本完成确认响应发送成功");
                } else {
                    log("❌ 脚本完成确认响应发送失败，状态码: " + (response ? response.statusCode : "null"));
                }

            } catch (e) {
                log("❌ 发送脚本完成确认响应时发生错误: " + e.message);
            }
        });

        log("脚本执行完成通知处理完成");

    } catch (e) {
        log("❌ 处理脚本执行完成通知时发生错误: " + e.message);
    }
}

// 处理关闭小红书应用命令
function handleCloseXiaohongshuAppCommand(command) {
    log("=== 处理PC端关闭小红书应用命令 ===");
    log("关闭原因: " + (command.reason || "未知"));

    try {
        log("开始关闭小红书应用，5秒后执行");
        toast("正在关闭小红书应用...");

        // 在后台线程中执行关闭操作
        threads.start(function() {
            try {
                sleep(5000);
                closeXiaohongshuApp();
                log("小红书应用关闭命令执行完成");

                ui.run(function() {
                    toast("小红书应用已关闭");
                });

                // 上报应用关闭状态到服务器
                log("上报小红书应用关闭状态到服务器");
                reportAppCloseStatus(command.reason || "服务器关闭");
            } catch (e) {
                log("关闭小红书应用失败: " + e.message);
                ui.run(function() {
                    toast("关闭小红书应用失败: " + e.message);
                });

                // 即使失败也要上报状态
                try {
                    reportAppCloseStatus("关闭失败: " + e.message);
                } catch (reportError) {
                    log("上报应用关闭状态失败: " + reportError.message);
                }
            }
        });

    } catch (e) {
        log("启动关闭小红书应用任务失败: " + e.message);
        toast("启动关闭小红书应用任务失败: " + e.message);
    }
}

// 上报脚本停止状态到服务器
function reportScriptStopStatus(reason) {
    log("=== 上报脚本停止状态 ===");
    log("停止原因: " + reason);

    try {
        var reportData = {
            deviceId: deviceId,
            status: "script_stopped",
            reason: reason,
            timestamp: new Date().toISOString(),
            message: "脚本已停止"
        };

        log("准备上报脚本停止数据: " + JSON.stringify(reportData));

        var response = http.postJson(serverUrl + "/api/device/" + deviceId + "/script-status", reportData, {
            headers: {
                "Authorization": "Bearer " + authToken,
                "Content-Type": "application/json"
            }
        });

        if (response.statusCode === 200) {
            log("✅ 脚本停止状态上报成功");
        } else {
            log("❌ 脚本停止状态上报失败，状态码: " + response.statusCode);
            log("响应内容: " + response.body);
        }

    } catch (e) {
        log("❌ 上报脚本停止状态时发生错误: " + e.message);
    }
}

// 上报应用关闭状态到服务器
function reportAppCloseStatus(reason) {
    log("=== 上报应用关闭状态 ===");
    log("关闭原因: " + reason);

    try {
        var reportData = {
            deviceId: deviceId,
            status: "app_closed",
            reason: reason,
            timestamp: new Date().toISOString(),
            message: "小红书应用已关闭"
        };

        log("准备上报数据: " + JSON.stringify(reportData));

        var response = http.postJson(serverUrl + "/api/device/" + deviceId + "/app-status", reportData, {
            headers: {
                "Authorization": "Bearer " + authToken,
                "Content-Type": "application/json"
            }
        });

        if (response.statusCode === 200) {
            log("✅ 应用关闭状态上报成功");
        } else {
            log("❌ 应用关闭状态上报失败，状态码: " + response.statusCode);
            log("响应内容: " + response.body);
        }

    } catch (e) {
        log("❌ 上报应用关闭状态时发生错误: " + e.message);
    }
}

// ==================== 关闭小红书应用 ====================

// 关闭小红书APP
function closeXiaohongshuApp() {
    log("开始关闭小红书APP");

    try {
        // 检查当前是否在小红书
        let currentPkg = currentPackage();
        log("当前应用包名: " + (currentPkg || "无"));

        if (currentPkg && currentPkg.includes("xhs")) {
            log("检测到小红书正在运行，开始关闭");

            // 方法1: 使用返回键多次退出
            log("使用返回键方式关闭应用");
            for (let i = 0; i < 15; i++) {
                back();
                sleep(300);

                // 检查是否已经退出到桌面
                let pkg = currentPackage();
                if (!pkg || pkg.includes("launcher") || pkg.includes("home") || pkg.includes("desktop")) {
                    log("✅ 已退出到桌面，当前应用: " + (pkg || "桌面"));
                    return true;
                }

                // 如果还在小红书，继续按返回键
                if (pkg && pkg.includes("xhs")) {
                    log("仍在小红书中，继续按返回键 (" + (i + 1) + "/15)");
                } else {
                    log("已切换到其他应用: " + pkg);
                    break;
                }
            }
        }

        // 方法2: 按Home键确保回到桌面
        log("按Home键返回桌面");
        home();
        sleep(1000);

        // 再次检查当前应用
        let finalPkg = currentPackage();
        log("最终应用包名: " + (finalPkg || "桌面"));

        if (!finalPkg || finalPkg.includes("launcher") || finalPkg.includes("home") || finalPkg.includes("desktop")) {
            log("✅ 小红书APP关闭成功");
        } else if (finalPkg.includes("xhs")) {
            log("⚠️ 小红书可能仍在运行，但已尽力关闭");
        } else {
            log("✅ 已切换到其他应用，小红书关闭成功");
        }

        return true;

    } catch (e) {
        log("❌ 关闭小红书APP时出错: " + e.message);
        // 备用方法：直接按Home键
        log("使用备用方法：按Home键");
        home();
        sleep(1000);
        return true;
    }
}

// 处理Token更新命令 - 已废弃，系统改为连接码模式
/*
function handleTokenUpdateCommand(command) {
    try {
        log("开始处理Token更新命令");

        if (!command.credentials) {
            log("❌ Token更新命令缺少凭据信息");
            return;
        }

        var newCredentials = command.credentials;
        log("新用户凭据:");
        log("- 用户名: " + newCredentials.username);
        log("- Token: " + (newCredentials.token ? "已提供" : "未提供"));

        // 更新全局token变量
        if (newCredentials.token) {
            token = newCredentials.token;
            log("✅ Token已更新");

            // 显示通知
            toast("设备Token已更新到用户: " + newCredentials.username);

            // 可选：保存新凭据到本地存储（如果需要持久化）
            // 这里可以使用Auto.js的存储API来保存凭据

            log("Token更新完成，设备现在属于用户: " + newCredentials.username);
        } else {
            log("❌ 新凭据中没有Token信息");
        }

    } catch (e) {
        log("❌ 处理Token更新命令时出错: " + e.message);
    }
}
*/

// 添加全局异常处理，防止脚本意外退出
try {
    // 设置全局异常处理器
    java.lang.Thread.setDefaultUncaughtExceptionHandler(
        new java.lang.Thread.UncaughtExceptionHandler({
            uncaughtException: function(thread, exception) {
                log("❌ 捕获到未处理的异常: " + exception.getMessage());
                log("异常线程: " + thread.getName());
                log("异常堆栈: " + exception.getStackTrace());

                // 不让异常导致脚本退出，只记录日志
                log("⚠️ 异常已被捕获，脚本继续运行");
            }
        })
    );
    log("✅ 全局异常处理器已设置");
} catch (e) {
    log("⚠️ 设置全局异常处理器失败: " + e.message);
}

// 添加脚本保护机制
var scriptProtection = {
    isProtected: true,
    lastHeartbeat: Date.now(),

    // 心跳检测，确保脚本持续运行
    startHeartbeat: function() {
        setInterval(function() {
            if (scriptProtection.isProtected) {
                scriptProtection.lastHeartbeat = Date.now();
                // 每5分钟输出一次心跳日志，证明脚本还在运行
                if (scriptProtection.lastHeartbeat % 300000 < 5000) {
                    log("💓 脚本心跳检测正常 - " + new Date().toLocaleTimeString());
                }
            }
        }, 5000); // 每5秒检测一次
    },

    // 停止保护（仅在用户主动退出时调用）
    stopProtection: function() {
        log("🛡️ 脚本保护已停止");
        scriptProtection.isProtected = false;
    }
};

// 启动脚本保护
scriptProtection.startHeartbeat();

log("🛡️ 脚本保护机制已启动");
log("双向断开通信测试已启动");
log("目标服务器: " + serverUrl);
log("请点击连接开始测试双向断开功能");
